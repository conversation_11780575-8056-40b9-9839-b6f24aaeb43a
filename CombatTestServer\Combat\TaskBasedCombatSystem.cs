using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestServer.Combat
{
    public class TaskBasedCombatSystem : ICombatSystem
    {
        public string Name => "Task-based Approach";
        public AttackApproach Approach => AttackApproach.TaskBased;
        public bool IsRunning { get; private set; }

        private readonly ConcurrentDictionary<string, CancellationTokenSource> _activeAttacks;
        private readonly IDamageProcessor _damageProcessor;
        private readonly CombatConfiguration _config;
        private readonly SemaphoreSlim _concurrencyLimiter;

        public event EventHandler<DamagePacket> DamageDealt;
        public event EventHandler<string> SystemMessage;

        public TaskBasedCombatSystem(IDamageProcessor damageProcessor, CombatConfiguration config)
        {
            _activeAttacks = new ConcurrentDictionary<string, CancellationTokenSource>();
            _damageProcessor = damageProcessor ?? throw new ArgumentNullException(nameof(damageProcessor));
            _config = config ?? new CombatConfiguration();
            _concurrencyLimiter = new SemaphoreSlim(Environment.ProcessorCount * 4, Environment.ProcessorCount * 4);
        }

        public Task StartAsync()
        {
            IsRunning = true;
            SystemMessage?.Invoke(this, $"{Name} started");
            return Task.CompletedTask;
        }

        public Task StopAsync()
        {
            IsRunning = false;
            
            // Cancel all active attacks
            foreach (var cts in _activeAttacks.Values)
            {
                cts.Cancel();
            }
            _activeAttacks.Clear();
            
            SystemMessage?.Invoke(this, $"{Name} stopped");
            return Task.CompletedTask;
        }

        public async Task<DamageResult> ExecuteAttackAsync(AttackCommand command)
        {
            if (!IsRunning)
                throw new InvalidOperationException("Combat system is not running");

            var cts = new CancellationTokenSource();
            if (!_activeAttacks.TryAdd(command.Id, cts))
                throw new InvalidOperationException($"Attack {command.Id} is already being executed");

            try
            {
                await _concurrencyLimiter.WaitAsync(cts.Token);
                
                var skill = GenerateSkill(command.SkillId);
                var result = await ExecuteAttackWithTasksAsync(command, skill, cts.Token);
                
                return result;
            }
            finally
            {
                _activeAttacks.TryRemove(command.Id, out _);
                _concurrencyLimiter.Release();
                cts.Dispose();
            }
        }

        private async Task<DamageResult> ExecuteAttackWithTasksAsync(AttackCommand command, Skill skill, CancellationToken cancellationToken)
        {
            var damagePackets = new ConcurrentBag<DamagePacket>();
            var hitTasks = new List<Task>();

            // Create tasks for each hit timing
            foreach (var hitTiming in skill.HitTimings)
            {
                var hitTask = ProcessHitAsync(command, skill, hitTiming, damagePackets, cancellationToken);
                hitTasks.Add(hitTask);
            }

            // Wait for all hit tasks to complete
            await Task.WhenAll(hitTasks);

            // Create result
            var result = new DamageResult
            {
                AttackId = command.Id,
                TotalDamageDealt = damagePackets.Sum(p => p.Damage),
                ExecutionTime = DateTime.UtcNow - command.StartTime,
                DamagePackets = damagePackets.ToArray()
            };

            return result;
        }

        private async Task ProcessHitAsync(AttackCommand command, Skill skill, HitTiming hitTiming, 
            ConcurrentBag<DamagePacket> damagePackets, CancellationToken cancellationToken)
        {
            try
            {
                // Wait for the hit timing
                await Task.Delay(hitTiming.DelayFromStart, cancellationToken);

                // Process damage for all targets in parallel
                var targetTasks = command.TargetMonsterIds.Select(async monsterId =>
                {
                    var damage = CalculateDamage(skill.BaseDamage, hitTiming.DamageMultiplier);
                    var packet = new DamagePacket(command.PlayerId, monsterId, skill.Id, hitTiming.HitIndex, damage)
                    {
                        ProcessingDelay = DateTime.UtcNow - command.StartTime
                    };

                    damagePackets.Add(packet);
                    await ProcessDamageAsync(packet);
                });

                await Task.WhenAll(targetTasks);
            }
            catch (OperationCanceledException)
            {
                // Attack was cancelled, this is expected
            }
            catch (Exception ex)
            {
                SystemMessage?.Invoke(this, $"Error in hit processing: {ex.Message}");
            }
        }

        private Skill GenerateSkill(int skillId)
        {
            var random = new Random();
            var hitCount = random.Next(_config.MinHitsPerSkill, _config.MaxHitsPerSkill + 1);
            var baseDamage = random.Next(_config.MinDamagePerHit, _config.MaxDamagePerHit + 1);
            
            return new Skill(skillId, $"Skill_{skillId}", baseDamage, hitCount);
        }

        private int CalculateDamage(int baseDamage, float multiplier)
        {
            var random = new Random();
            var damage = (int)(baseDamage * multiplier);
            
            // Apply critical hit chance
            if (random.NextDouble() < _config.CriticalChance)
            {
                damage = (int)(damage * _config.CriticalMultiplier);
            }

            return damage;
        }

        private async Task ProcessDamageAsync(DamagePacket packet)
        {
            await _damageProcessor.ProcessDamageAsync(packet);
            DamageDealt?.Invoke(this, packet);
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _concurrencyLimiter?.Dispose();
        }
    }
}
