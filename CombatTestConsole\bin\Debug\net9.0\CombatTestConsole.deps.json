{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"CombatTestConsole/1.0.0": {"dependencies": {"CombatTestClient": "1.0.0", "CombatTestPerformance": "1.0.0", "CombatTestServer": "1.0.0", "CombatTestShared": "1.0.0"}, "runtime": {"CombatTestConsole.dll": {}}}, "CombatTestClient/1.0.0": {"dependencies": {"CombatTestShared": "1.0.0"}, "runtime": {"CombatTestClient.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "CombatTestPerformance/1.0.0": {"dependencies": {"CombatTestShared": "1.0.0"}, "runtime": {"CombatTestPerformance.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "CombatTestServer/1.0.0": {"dependencies": {"CombatTestShared": "1.0.0"}, "runtime": {"CombatTestServer.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "CombatTestShared/1.0.0": {"runtime": {"CombatTestShared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"CombatTestConsole/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CombatTestClient/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CombatTestPerformance/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CombatTestServer/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CombatTestShared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}