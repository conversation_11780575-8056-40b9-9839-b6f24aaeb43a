using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestServer.Combat
{
    public class CentralizedTimerCombatSystem : ICombatSystem
    {
        public string Name => "Centralized Timer Approach";
        public AttackApproach Approach => AttackApproach.CentralizedTimer;
        public bool IsRunning { get; private set; }

        private readonly ConcurrentDictionary<string, AttackExecution> _activeAttacks;
        private readonly ConcurrentQueue<ScheduledHit> _damageQueue;
        private readonly IDamageProcessor _damageProcessor;
        private readonly CombatConfiguration _config;
        private readonly System.Timers.Timer _centralTimer;
        private readonly object _timerLock = new object();

        public event EventHandler<DamagePacket> DamageDealt;
        public event EventHandler<string> SystemMessage;

        private const int TIMER_INTERVAL_MS = 10; // 10ms tick rate for high precision

        public CentralizedTimerCombatSystem(IDamageProcessor damageProcessor, CombatConfiguration config)
        {
            _activeAttacks = new ConcurrentDictionary<string, AttackExecution>();
            _damageQueue = new ConcurrentQueue<ScheduledHit>();
            _damageProcessor = damageProcessor ?? throw new ArgumentNullException(nameof(damageProcessor));
            _config = config ?? new CombatConfiguration();
            
            _centralTimer = new System.Timers.Timer(TIMER_INTERVAL_MS);
            _centralTimer.Elapsed += OnCentralTimerTick;
            _centralTimer.AutoReset = true;
        }

        public Task StartAsync()
        {
            IsRunning = true;
            _centralTimer.Start();
            SystemMessage?.Invoke(this, $"{Name} started with {TIMER_INTERVAL_MS}ms tick rate");
            return Task.CompletedTask;
        }

        public Task StopAsync()
        {
            IsRunning = false;
            _centralTimer.Stop();
            
            // Clear all active attacks and damage queue
            _activeAttacks.Clear();
            while (_damageQueue.TryDequeue(out _)) { }
            
            SystemMessage?.Invoke(this, $"{Name} stopped");
            return Task.CompletedTask;
        }

        public async Task<DamageResult> ExecuteAttackAsync(AttackCommand command)
        {
            if (!IsRunning)
                throw new InvalidOperationException("Combat system is not running");

            var skill = GenerateSkill(command.SkillId);
            var attackExecution = new AttackExecution(command, skill);
            
            if (_activeAttacks.TryAdd(command.Id, attackExecution))
            {
                // Schedule all hits in the damage queue
                ScheduleHits(command, skill, attackExecution);
                
                // Wait for completion
                var result = await attackExecution.WaitForCompletionAsync();
                _activeAttacks.TryRemove(command.Id, out _);
                
                return result;
            }

            throw new InvalidOperationException($"Attack {command.Id} is already being executed");
        }

        private void ScheduleHits(AttackCommand command, Skill skill, AttackExecution execution)
        {
            var baseTime = DateTime.UtcNow;
            
            foreach (var hitTiming in skill.HitTimings)
            {
                var executeTime = baseTime.Add(hitTiming.DelayFromStart);
                
                foreach (var monsterId in command.TargetMonsterIds)
                {
                    var scheduledHit = new ScheduledHit
                    {
                        AttackId = command.Id,
                        PlayerId = command.PlayerId,
                        MonsterId = monsterId,
                        SkillId = skill.Id,
                        HitIndex = hitTiming.HitIndex,
                        BaseDamage = skill.BaseDamage,
                        DamageMultiplier = hitTiming.DamageMultiplier,
                        ExecuteTime = executeTime,
                        Execution = execution
                    };
                    
                    _damageQueue.Enqueue(scheduledHit);
                }
            }
        }

        private async void OnCentralTimerTick(object sender, ElapsedEventArgs e)
        {
            if (!IsRunning) return;

            var currentTime = DateTime.UtcNow;
            var hitsToProcess = new List<ScheduledHit>();
            var tempQueue = new List<ScheduledHit>();

            // Collect hits that are ready to execute
            while (_damageQueue.TryDequeue(out var hit))
            {
                if (hit.ExecuteTime <= currentTime)
                {
                    hitsToProcess.Add(hit);
                }
                else
                {
                    tempQueue.Add(hit);
                }
            }

            // Re-queue hits that aren't ready yet
            foreach (var hit in tempQueue)
            {
                _damageQueue.Enqueue(hit);
            }

            // Process ready hits in parallel
            if (hitsToProcess.Count > 0)
            {
                var processingTasks = hitsToProcess.Select(ProcessScheduledHitAsync);
                await Task.WhenAll(processingTasks);
            }
        }

        private async Task ProcessScheduledHitAsync(ScheduledHit hit)
        {
            try
            {
                var damage = CalculateDamage(hit.BaseDamage, hit.DamageMultiplier);
                var packet = new DamagePacket(hit.PlayerId, hit.MonsterId, hit.SkillId, hit.HitIndex, damage)
                {
                    ProcessingDelay = DateTime.UtcNow - hit.ExecuteTime
                };

                await _damageProcessor.ProcessDamageAsync(packet);
                DamageDealt?.Invoke(this, packet);

                // Notify the attack execution
                hit.Execution.RecordHit(packet);
            }
            catch (Exception ex)
            {
                SystemMessage?.Invoke(this, $"Error processing scheduled hit: {ex.Message}");
            }
        }

        private Skill GenerateSkill(int skillId)
        {
            var random = new Random();
            var hitCount = random.Next(_config.MinHitsPerSkill, _config.MaxHitsPerSkill + 1);
            var baseDamage = random.Next(_config.MinDamagePerHit, _config.MaxDamagePerHit + 1);
            
            return new Skill(skillId, $"Skill_{skillId}", baseDamage, hitCount);
        }

        private int CalculateDamage(int baseDamage, float multiplier)
        {
            var random = new Random();
            var damage = (int)(baseDamage * multiplier);
            
            // Apply critical hit chance
            if (random.NextDouble() < _config.CriticalChance)
            {
                damage = (int)(damage * _config.CriticalMultiplier);
            }

            return damage;
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _centralTimer?.Dispose();
        }

        private class ScheduledHit
        {
            public string AttackId { get; set; }
            public int PlayerId { get; set; }
            public int MonsterId { get; set; }
            public int SkillId { get; set; }
            public int HitIndex { get; set; }
            public int BaseDamage { get; set; }
            public float DamageMultiplier { get; set; }
            public DateTime ExecuteTime { get; set; }
            public AttackExecution Execution { get; set; }
        }

        private class AttackExecution
        {
            private readonly AttackCommand _command;
            private readonly Skill _skill;
            private readonly List<DamagePacket> _damagePackets;
            private readonly TaskCompletionSource<DamageResult> _completionSource;
            private int _expectedHits;
            private int _completedHits;

            public AttackExecution(AttackCommand command, Skill skill)
            {
                _command = command;
                _skill = skill;
                _damagePackets = new List<DamagePacket>();
                _completionSource = new TaskCompletionSource<DamageResult>();
                _expectedHits = skill.HitCount * command.TargetMonsterIds.Length;
                _completedHits = 0;
            }

            public void RecordHit(DamagePacket packet)
            {
                lock (_damagePackets)
                {
                    _damagePackets.Add(packet);
                    
                    if (Interlocked.Increment(ref _completedHits) >= _expectedHits)
                    {
                        var result = new DamageResult
                        {
                            AttackId = _command.Id,
                            TotalDamageDealt = _damagePackets.Sum(p => p.Damage),
                            ExecutionTime = DateTime.UtcNow - _command.StartTime,
                            DamagePackets = _damagePackets.ToArray()
                        };

                        _completionSource.TrySetResult(result);
                    }
                }
            }

            public async Task<DamageResult> WaitForCompletionAsync()
            {
                // Set timeout based on skill duration plus buffer
                var timeout = _skill.Duration.Add(TimeSpan.FromSeconds(2));
                var timeoutTask = Task.Delay(timeout);
                
                var completedTask = await Task.WhenAny(_completionSource.Task, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    _completionSource.TrySetException(new TimeoutException($"Attack {_command.Id} timed out"));
                }

                return await _completionSource.Task;
            }
        }
    }
}
