using System;
using System.Collections.Generic;

namespace CombatTestShared.Models
{
    public class Skill
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int BaseDamage { get; set; }
        public int HitCount { get; set; }
        public TimeSpan Duration { get; set; }
        public List<HitTiming> HitTimings { get; set; }
        public SkillType Type { get; set; }
        public int MaxTargets { get; set; }
        public TimeSpan Cooldown { get; set; }

        public Skill()
        {
            HitTimings = new List<HitTiming>();
            Duration = TimeSpan.FromSeconds(2); // Default 2 seconds
            MaxTargets = 10; // Default max 10 targets
        }

        public Skill(int id, string name, int baseDamage, int hitCount) : this()
        {
            Id = id;
            Name = name;
            BaseDamage = baseDamage;
            HitCount = hitCount;
            
            // Generate random hit timings within the duration
            GenerateHitTimings();
        }

        private void GenerateHitTimings()
        {
            HitTimings.Clear();
            var random = new Random();
            
            for (int i = 0; i < HitCount; i++)
            {
                var timing = new HitTiming
                {
                    HitIndex = i,
                    DelayFromStart = TimeSpan.FromMilliseconds(
                        random.Next(0, (int)Duration.TotalMilliseconds)),
                    DamageMultiplier = 0.8f + (float)random.NextDouble() * 0.4f // 0.8 to 1.2
                };
                HitTimings.Add(timing);
            }
            
            // Sort by delay to ensure proper timing order
            HitTimings.Sort((a, b) => a.DelayFromStart.CompareTo(b.DelayFromStart));
        }
    }

    public class HitTiming
    {
        public int HitIndex { get; set; }
        public TimeSpan DelayFromStart { get; set; }
        public float DamageMultiplier { get; set; }
        public bool HasExecuted { get; set; }

        public HitTiming()
        {
            HasExecuted = false;
            DamageMultiplier = 1.0f;
        }
    }

    public enum SkillType
    {
        SingleTarget,
        MultiTarget,
        AreaOfEffect,
        Channeled
    }
}
