﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CombatTestServer", "CombatTestServer\CombatTestServer.csproj", "{6874A712-5272-4A59-A9C2-5A5A6C638649}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CombatTestClient", "CombatTestClient\CombatTestClient.csproj", "{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CombatTestShared", "CombatTestShared\CombatTestShared.csproj", "{A810AC4D-F3A8-4BAC-BE65-F806AB827114}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CombatTestPerformance", "CombatTestPerformance\CombatTestPerformance.csproj", "{334873C8-EDD7-4D5A-896D-3DBFB6657522}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CombatTestConsole", "CombatTestConsole\CombatTestConsole.csproj", "{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|x64.Build.0 = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Debug|x86.Build.0 = Debug|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|Any CPU.Build.0 = Release|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|x64.ActiveCfg = Release|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|x64.Build.0 = Release|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|x86.ActiveCfg = Release|Any CPU
		{6874A712-5272-4A59-A9C2-5A5A6C638649}.Release|x86.Build.0 = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|x64.Build.0 = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Debug|x86.Build.0 = Debug|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|x64.ActiveCfg = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|x64.Build.0 = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|x86.ActiveCfg = Release|Any CPU
		{F6FBAA93-BA40-481F-BFB1-D813359B8BBB}.Release|x86.Build.0 = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|x64.Build.0 = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Debug|x86.Build.0 = Debug|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|Any CPU.Build.0 = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|x64.ActiveCfg = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|x64.Build.0 = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|x86.ActiveCfg = Release|Any CPU
		{A810AC4D-F3A8-4BAC-BE65-F806AB827114}.Release|x86.Build.0 = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|x64.ActiveCfg = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|x64.Build.0 = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|x86.ActiveCfg = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Debug|x86.Build.0 = Debug|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|Any CPU.Build.0 = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|x64.ActiveCfg = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|x64.Build.0 = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|x86.ActiveCfg = Release|Any CPU
		{334873C8-EDD7-4D5A-896D-3DBFB6657522}.Release|x86.Build.0 = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|x64.Build.0 = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Debug|x86.Build.0 = Debug|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|x64.ActiveCfg = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|x64.Build.0 = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|x86.ActiveCfg = Release|Any CPU
		{7A97B50B-0E66-448B-9C1D-FD4E79D974E1}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
