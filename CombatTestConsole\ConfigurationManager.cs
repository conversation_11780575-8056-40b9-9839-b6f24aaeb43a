using System;
using System.IO;
using System.Text.Json;
using CombatTestShared.Models;

namespace CombatTestConsole
{
    public class ConfigurationManager
    {
        private const string DEFAULT_CONFIG_FILE = "appsettings.json";
        private const string CUSTOM_CONFIG_FILE = "custom-config.json";

        public static TestConfiguration LoadConfiguration(string configFile = null)
        {
            var fileName = configFile ?? DEFAULT_CONFIG_FILE;
            
            if (File.Exists(fileName))
            {
                try
                {
                    var json = File.ReadAllText(fileName);
                    var config = JsonSerializer.Deserialize<TestConfiguration>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true
                    });
                    
                    Console.WriteLine($"Configuration loaded from {fileName}");
                    return config;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading configuration from {fileName}: {ex.Message}");
                    Console.WriteLine("Using default configuration...");
                }
            }
            else
            {
                Console.WriteLine($"Configuration file {fileName} not found. Using default configuration...");
            }

            return CreateDefaultConfiguration();
        }

        public static void SaveConfiguration(TestConfiguration config, string configFile = null)
        {
            var fileName = configFile ?? CUSTOM_CONFIG_FILE;
            
            try
            {
                var json = JsonSerializer.Serialize(config, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                
                File.WriteAllText(fileName, json);
                Console.WriteLine($"Configuration saved to {fileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving configuration to {fileName}: {ex.Message}");
            }
        }

        public static TestConfiguration CreateDefaultConfiguration()
        {
            return new TestConfiguration
            {
                PlayerCount = 500,
                MonsterCountPerPlayer = 10,
                SkillsPerPlayer = 3,
                TestDuration = TimeSpan.FromMinutes(2),
                AttackInterval = TimeSpan.FromSeconds(3),
                EnablePerformanceMonitoring = true,
                EnableDetailedLogging = false,
                ApproachesToTest = new[] 
                { 
                    AttackApproach.IndividualTimer, 
                    AttackApproach.TaskBased, 
                    AttackApproach.CentralizedTimer 
                },
                Network = new NetworkConfiguration
                {
                    ServerHost = "localhost",
                    ServerPort = 8080,
                    MaxConnections = 1000,
                    ConnectionTimeout = TimeSpan.FromSeconds(30),
                    BufferSize = 4096,
                    UseNagleAlgorithm = false
                },
                Combat = new CombatConfiguration
                {
                    MinHitsPerSkill = 1,
                    MaxHitsPerSkill = 5,
                    MinDamagePerHit = 50,
                    MaxDamagePerHit = 200,
                    CriticalChance = 0.15f,
                    CriticalMultiplier = 2.0f,
                    SkillDuration = TimeSpan.FromSeconds(2),
                    RandomizeHitTimings = true
                }
            };
        }

        public static TestConfiguration CreateHighLoadConfiguration()
        {
            var config = CreateDefaultConfiguration();
            config.PlayerCount = 1000;
            config.MonsterCountPerPlayer = 15;
            config.TestDuration = TimeSpan.FromMinutes(5);
            config.AttackInterval = TimeSpan.FromSeconds(2);
            config.Combat.MaxHitsPerSkill = 7;
            return config;
        }

        public static TestConfiguration CreateStressTestConfiguration()
        {
            var config = CreateDefaultConfiguration();
            config.PlayerCount = 1000;
            config.MonsterCountPerPlayer = 20;
            config.TestDuration = TimeSpan.FromMinutes(10);
            config.AttackInterval = TimeSpan.FromSeconds(1);
            config.Combat.MaxHitsPerSkill = 10;
            config.EnableDetailedLogging = true;
            return config;
        }

        public static TestConfiguration CreateQuickTestConfiguration()
        {
            var config = CreateDefaultConfiguration();
            config.PlayerCount = 100;
            config.MonsterCountPerPlayer = 5;
            config.TestDuration = TimeSpan.FromSeconds(30);
            config.AttackInterval = TimeSpan.FromSeconds(2);
            return config;
        }

        public static void PrintConfiguration(TestConfiguration config)
        {
            Console.WriteLine("CURRENT CONFIGURATION:");
            Console.WriteLine(new string('=', 50));
            Console.WriteLine($"Player Count: {config.PlayerCount}");
            Console.WriteLine($"Monsters per Player: {config.MonsterCountPerPlayer}");
            Console.WriteLine($"Skills per Player: {config.SkillsPerPlayer}");
            Console.WriteLine($"Test Duration: {config.TestDuration}");
            Console.WriteLine($"Attack Interval: {config.AttackInterval}");
            Console.WriteLine($"Performance Monitoring: {config.EnablePerformanceMonitoring}");
            Console.WriteLine($"Detailed Logging: {config.EnableDetailedLogging}");
            Console.WriteLine($"Approaches to Test: {string.Join(", ", config.ApproachesToTest)}");
            Console.WriteLine();
            
            Console.WriteLine("NETWORK CONFIGURATION:");
            Console.WriteLine($"  Server: {config.Network.ServerHost}:{config.Network.ServerPort}");
            Console.WriteLine($"  Max Connections: {config.Network.MaxConnections}");
            Console.WriteLine($"  Connection Timeout: {config.Network.ConnectionTimeout}");
            Console.WriteLine($"  Buffer Size: {config.Network.BufferSize} bytes");
            Console.WriteLine($"  Nagle Algorithm: {config.Network.UseNagleAlgorithm}");
            Console.WriteLine();
            
            Console.WriteLine("COMBAT CONFIGURATION:");
            Console.WriteLine($"  Hits per Skill: {config.Combat.MinHitsPerSkill}-{config.Combat.MaxHitsPerSkill}");
            Console.WriteLine($"  Damage per Hit: {config.Combat.MinDamagePerHit}-{config.Combat.MaxDamagePerHit}");
            Console.WriteLine($"  Critical Chance: {config.Combat.CriticalChance:P1}");
            Console.WriteLine($"  Critical Multiplier: {config.Combat.CriticalMultiplier:F1}x");
            Console.WriteLine($"  Skill Duration: {config.Combat.SkillDuration}");
            Console.WriteLine($"  Randomize Hit Timings: {config.Combat.RandomizeHitTimings}");
            Console.WriteLine();
        }

        public static TestConfiguration ModifyConfiguration(TestConfiguration config)
        {
            Console.WriteLine("CONFIGURATION MODIFICATION:");
            Console.WriteLine("Press Enter to keep current value, or enter new value:");
            Console.WriteLine();

            config.PlayerCount = PromptForInt("Player Count", config.PlayerCount, 1, 2000);
            config.MonsterCountPerPlayer = PromptForInt("Monsters per Player", config.MonsterCountPerPlayer, 1, 50);
            config.SkillsPerPlayer = PromptForInt("Skills per Player", config.SkillsPerPlayer, 1, 10);
            
            var testDurationMinutes = PromptForInt("Test Duration (minutes)", (int)config.TestDuration.TotalMinutes, 1, 60);
            config.TestDuration = TimeSpan.FromMinutes(testDurationMinutes);
            
            var attackIntervalSeconds = PromptForInt("Attack Interval (seconds)", (int)config.AttackInterval.TotalSeconds, 1, 30);
            config.AttackInterval = TimeSpan.FromSeconds(attackIntervalSeconds);

            return config;
        }

        private static int PromptForInt(string prompt, int currentValue, int min, int max)
        {
            while (true)
            {
                Console.Write($"{prompt} (current: {currentValue}, range: {min}-{max}): ");
                var input = Console.ReadLine();
                
                if (string.IsNullOrWhiteSpace(input))
                    return currentValue;
                
                if (int.TryParse(input, out var value) && value >= min && value <= max)
                    return value;
                
                Console.WriteLine($"Invalid input. Please enter a number between {min} and {max}.");
            }
        }
    }
}
