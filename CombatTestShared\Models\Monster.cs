using System;

namespace CombatTestShared.Models
{
    public class Monster
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Health { get; set; }
        public int MaxHealth { get; set; }
        public MonsterPosition Position { get; set; }
        public MonsterType Type { get; set; }
        public bool IsAlive { get; set; }
        public DateTime SpawnTime { get; set; }

        public Monster()
        {
            Position = new MonsterPosition();
            IsAlive = true;
            SpawnTime = DateTime.UtcNow;
        }

        public Monster(int id, string name, MonsterType type) : this()
        {
            Id = id;
            Name = name;
            Type = type;
            
            // Set health based on monster type
            switch (type)
            {
                case MonsterType.Normal:
                    Health = MaxHealth = 500;
                    break;
                case MonsterType.Elite:
                    Health = MaxHealth = 1000;
                    break;
                case MonsterType.Boss:
                    Health = MaxHealth = 5000;
                    break;
                default:
                    Health = MaxHealth = 500;
                    break;
            }
        }

        public void TakeDamage(int damage)
        {
            Health = Math.Max(0, Health - damage);
            if (Health <= 0)
            {
                IsAlive = false;
            }
        }
    }

    public class MonsterPosition
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public MonsterPosition() { }

        public MonsterPosition(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
    }

    public enum MonsterType
    {
        Normal,
        Elite,
        Boss
    }
}
