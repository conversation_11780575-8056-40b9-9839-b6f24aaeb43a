using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestServer.Combat
{
    public class DamageProcessor : IDamageProcessor
    {
        private readonly ConcurrentDictionary<int, Monster> _monsters;
        private readonly IConnectionManager _connectionManager;
        private long _totalDamageProcessed;
        private long _totalPacketsProcessed;

        public long TotalDamageProcessed => _totalDamageProcessed;
        public long TotalPacketsProcessed => _totalPacketsProcessed;

        public event EventHandler<DamagePacket> DamageProcessed;
        public event EventHandler<Monster> MonsterKilled;

        public DamageProcessor(IConnectionManager connectionManager)
        {
            _monsters = new ConcurrentDictionary<int, Monster>();
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            InitializeMonsters();
        }

        private void InitializeMonsters()
        {
            // Initialize monsters for testing (1000 monsters per default)
            for (int i = 1; i <= 1000; i++)
            {
                var monsterType = i % 10 == 0 ? MonsterType.Elite : 
                                 i % 100 == 0 ? MonsterType.Boss : MonsterType.Normal;
                
                var monster = new Monster(i, $"Monster_{i}", monsterType);
                _monsters.TryAdd(i, monster);
            }
        }

        public async Task ProcessDamageAsync(DamagePacket packet)
        {
            try
            {
                // Apply damage to monster
                if (_monsters.TryGetValue(packet.MonsterId, out var monster))
                {
                    var originalHealth = monster.Health;
                    monster.TakeDamage(packet.Damage);
                    
                    // Update statistics
                    Interlocked.Add(ref _totalDamageProcessed, packet.Damage);
                    Interlocked.Increment(ref _totalPacketsProcessed);

                    // Check if monster was killed
                    if (!monster.IsAlive && originalHealth > 0)
                    {
                        MonsterKilled?.Invoke(this, monster);
                    }

                    // Send damage packet to client
                    await SendDamagePacketToClient(packet);
                    
                    DamageProcessed?.Invoke(this, packet);
                }
                else
                {
                    Console.WriteLine($"Monster {packet.MonsterId} not found for damage packet {packet.Id}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing damage packet {packet.Id}: {ex.Message}");
            }
        }

        public async Task ProcessBatchDamageAsync(DamagePacket[] packets)
        {
            var tasks = packets.Select(ProcessDamageAsync);
            await Task.WhenAll(tasks);
        }

        private async Task SendDamagePacketToClient(DamagePacket packet)
        {
            try
            {
                // Find the player's connection
                var connectionId = $"Client_{packet.PlayerId}";
                
                // Create damage display message
                var damageMessage = new
                {
                    Type = "DamageDealt",
                    PacketId = packet.Id,
                    PlayerId = packet.PlayerId,
                    MonsterId = packet.MonsterId,
                    Damage = packet.Damage,
                    IsCritical = packet.IsCritical,
                    Timestamp = packet.Timestamp,
                    ProcessingDelay = packet.ProcessingDelay.TotalMilliseconds
                };

                await _connectionManager.SendToClientAsync(connectionId, damageMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending damage packet to client: {ex.Message}");
            }
        }

        public Monster GetMonster(int monsterId)
        {
            _monsters.TryGetValue(monsterId, out var monster);
            return monster;
        }

        public void ResetMonster(int monsterId)
        {
            if (_monsters.TryGetValue(monsterId, out var monster))
            {
                monster.Health = monster.MaxHealth;
                monster.IsAlive = true;
            }
        }

        public void ResetAllMonsters()
        {
            foreach (var monster in _monsters.Values)
            {
                monster.Health = monster.MaxHealth;
                monster.IsAlive = true;
            }
        }

        public int GetAliveMonsterCount()
        {
            return _monsters.Values.Count(m => m.IsAlive);
        }

        public int GetTotalMonsterCount()
        {
            return _monsters.Count;
        }

        public void ClearStatistics()
        {
            Interlocked.Exchange(ref _totalDamageProcessed, 0);
            Interlocked.Exchange(ref _totalPacketsProcessed, 0);
        }
    }
}
