using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestPerformance
{
    public class PerformanceMonitor : IPerformanceMonitor
    {
        private readonly ConcurrentQueue<PerformanceMetrics> _metricsHistory;
        private readonly ConcurrentQueue<DamagePacket> _damagePackets;
        private readonly ConcurrentQueue<AttackCommand> _attackCommands;
        private readonly System.Timers.Timer _samplingTimer;
        private readonly Process _currentProcess;
        private readonly Stopwatch _uptime;
        
        private bool _isMonitoring;
        private long _totalDamagePackets;
        private long _totalAttacks;
        private long _totalSystemEvents;
        private DateTime _monitoringStartTime;

        public bool IsMonitoring => _isMonitoring;

        public PerformanceMonitor()
        {
            _metricsHistory = new ConcurrentQueue<PerformanceMetrics>();
            _damagePackets = new ConcurrentQueue<DamagePacket>();
            _attackCommands = new ConcurrentQueue<AttackCommand>();
            _currentProcess = Process.GetCurrentProcess();
            _uptime = new Stopwatch();
            
            _samplingTimer = new System.Timers.Timer(1000); // Sample every second
            _samplingTimer.Elapsed += OnSamplingTick;
            _samplingTimer.AutoReset = true;
        }

        public void StartMonitoring()
        {
            if (_isMonitoring) return;

            _isMonitoring = true;
            _monitoringStartTime = DateTime.UtcNow;
            _uptime.Start();
            _samplingTimer.Start();
            
            Console.WriteLine("Performance monitoring started");
        }

        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            _isMonitoring = false;
            _samplingTimer.Stop();
            _uptime.Stop();
            
            Console.WriteLine("Performance monitoring stopped");
        }

        public PerformanceMetrics GetCurrentMetrics()
        {
            return CollectMetrics();
        }

        public PerformanceReport GenerateReport(TimeSpan duration)
        {
            var metrics = new List<PerformanceMetrics>();
            
            // Collect all metrics from history
            while (_metricsHistory.TryDequeue(out var metric))
            {
                metrics.Add(metric);
            }

            if (metrics.Count == 0)
            {
                return new PerformanceReport
                {
                    TestDuration = duration,
                    TotalDamagePackets = _totalDamagePackets,
                    TotalAttacks = _totalAttacks
                };
            }

            // Calculate averages and peaks
            var avgMetrics = new PerformanceMetrics
            {
                CpuUsagePercent = metrics.Average(m => m.CpuUsagePercent),
                MemoryUsageMB = (long)metrics.Average(m => m.MemoryUsageMB),
                AverageLatencyMs = metrics.Average(m => m.AverageLatencyMs),
                ThroughputPacketsPerSecond = metrics.Average(m => m.ThroughputPacketsPerSecond),
                GCCollections = (long)metrics.Average(m => m.GCCollections)
            };

            var peakMetrics = new PerformanceMetrics
            {
                CpuUsagePercent = metrics.Max(m => m.CpuUsagePercent),
                MemoryUsageMB = metrics.Max(m => m.MemoryUsageMB),
                AverageLatencyMs = metrics.Max(m => m.AverageLatencyMs),
                ThroughputPacketsPerSecond = metrics.Max(m => m.ThroughputPacketsPerSecond),
                GCCollections = metrics.Max(m => m.GCCollections)
            };

            return new PerformanceReport
            {
                AverageMetrics = avgMetrics,
                PeakMetrics = peakMetrics,
                DetailedMetrics = metrics,
                TestDuration = duration,
                TotalDamagePackets = _totalDamagePackets,
                TotalAttacks = _totalAttacks,
                PacketsPerSecond = _totalDamagePackets / duration.TotalSeconds,
                AttacksPerSecond = _totalAttacks / duration.TotalSeconds
            };
        }

        public void RecordDamagePacket(DamagePacket packet)
        {
            if (!_isMonitoring) return;

            _damagePackets.Enqueue(packet);
            Interlocked.Increment(ref _totalDamagePackets);
        }

        public void RecordAttackCommand(AttackCommand command)
        {
            if (!_isMonitoring) return;

            _attackCommands.Enqueue(command);
            Interlocked.Increment(ref _totalAttacks);
        }

        public void RecordSystemEvent(string eventName, object data)
        {
            if (!_isMonitoring) return;

            Interlocked.Increment(ref _totalSystemEvents);
            Console.WriteLine($"System Event: {eventName} - {data}");
        }

        private void OnSamplingTick(object sender, ElapsedEventArgs e)
        {
            if (!_isMonitoring) return;

            var metrics = CollectMetrics();
            _metricsHistory.Enqueue(metrics);

            // Keep only last 1000 samples to prevent memory issues
            if (_metricsHistory.Count > 1000)
            {
                _metricsHistory.TryDequeue(out _);
            }
        }

        private PerformanceMetrics CollectMetrics()
        {
            try
            {
                _currentProcess.Refresh();

                var metrics = new PerformanceMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsagePercent = GetCpuUsage(),
                    MemoryUsageMB = _currentProcess.WorkingSet64 / (1024 * 1024),
                    TotalDamagePacketsSent = _totalDamagePackets,
                    TotalAttacksProcessed = _totalAttacks,
                    AverageLatencyMs = CalculateAverageLatency(),
                    ThroughputPacketsPerSecond = CalculateThroughput(),
                    GCCollections = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2),
                    Uptime = _uptime.Elapsed
                };

                return metrics;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting metrics: {ex.Message}");
                return new PerformanceMetrics();
            }
        }

        private double GetCpuUsage()
        {
            try
            {
                // Simple CPU usage calculation
                // In production, you might want to use PerformanceCounter for more accuracy
                return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
            }
            catch
            {
                return 0;
            }
        }

        private double CalculateAverageLatency()
        {
            var latencies = new List<double>();
            var tempPackets = new List<DamagePacket>();

            // Collect recent packets
            while (_damagePackets.TryDequeue(out var packet) && latencies.Count < 100)
            {
                latencies.Add(packet.ProcessingDelay.TotalMilliseconds);
                tempPackets.Add(packet);
            }

            // Re-queue some packets for next calculation
            foreach (var packet in tempPackets.Take(50))
            {
                _damagePackets.Enqueue(packet);
            }

            return latencies.Count > 0 ? latencies.Average() : 0;
        }

        private double CalculateThroughput()
        {
            var now = DateTime.UtcNow;
            var oneSecondAgo = now.AddSeconds(-1);
            
            var recentPackets = 0;
            var tempPackets = new List<DamagePacket>();

            while (_damagePackets.TryDequeue(out var packet))
            {
                if (packet.Timestamp >= oneSecondAgo)
                {
                    recentPackets++;
                }
                tempPackets.Add(packet);
            }

            // Re-queue recent packets
            foreach (var packet in tempPackets.Where(p => p.Timestamp >= oneSecondAgo))
            {
                _damagePackets.Enqueue(packet);
            }

            return recentPackets;
        }

        public void Dispose()
        {
            StopMonitoring();
            _samplingTimer?.Dispose();
            _currentProcess?.Dispose();
        }
    }
}
