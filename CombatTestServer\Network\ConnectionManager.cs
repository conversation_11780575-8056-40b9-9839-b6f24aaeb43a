using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
using System.Text;
using System.Text.Json;
using CombatTestShared.Interfaces;
using CombatTestShared.Models;

namespace CombatTestServer.Network
{
    public class ConnectionManager : IConnectionManager
    {
        private readonly ConcurrentDictionary<string, ClientConnection> _connections;
        private readonly NetworkConfiguration _config;
        private volatile bool _isRunning;

        public int ActiveConnections => _connections.Count;

        public event EventHandler<string> ClientConnected;
        public event EventHandler<string> ClientDisconnected;

        public ConnectionManager(NetworkConfiguration config)
        {
            _connections = new ConcurrentDictionary<string, ClientConnection>();
            _config = config ?? new NetworkConfiguration();
            _isRunning = false;
        }

        public async Task<bool> AcceptConnectionAsync(string connectionId)
        {
            if (!_isRunning || _connections.Count >= _config.MaxConnections)
                return false;

            var connection = new ClientConnection(connectionId);
            if (_connections.TryAdd(connectionId, connection))
            {
                ClientConnected?.Invoke(this, connectionId);
                Console.WriteLine($"Client connected: {connectionId} (Total: {ActiveConnections})");
                return true;
            }

            return false;
        }

        public async Task DisconnectAsync(string connectionId)
        {
            if (_connections.TryRemove(connectionId, out var connection))
            {
                connection.Dispose();
                ClientDisconnected?.Invoke(this, connectionId);
                Console.WriteLine($"Client disconnected: {connectionId} (Total: {ActiveConnections})");
            }
        }

        public async Task SendToClientAsync(string connectionId, object data)
        {
            if (_connections.TryGetValue(connectionId, out var connection))
            {
                await connection.SendAsync(data);
            }
        }

        public async Task BroadcastAsync(object data)
        {
            var tasks = new List<Task>();
            foreach (var connection in _connections.Values)
            {
                tasks.Add(connection.SendAsync(data));
            }
            await Task.WhenAll(tasks);
        }

        public void Start()
        {
            _isRunning = true;
        }

        public void Stop()
        {
            _isRunning = false;
            
            // Disconnect all clients
            var disconnectTasks = new List<Task>();
            foreach (var connectionId in _connections.Keys)
            {
                disconnectTasks.Add(DisconnectAsync(connectionId));
            }
            Task.WaitAll(disconnectTasks.ToArray());
        }
    }

    public class ClientConnection : IDisposable
    {
        public string ConnectionId { get; }
        public DateTime ConnectedAt { get; }
        public bool IsConnected { get; private set; }
        
        private readonly object _sendLock = new object();
        private bool _disposed = false;

        public ClientConnection(string connectionId)
        {
            ConnectionId = connectionId;
            ConnectedAt = DateTime.UtcNow;
            IsConnected = true;
        }

        public async Task SendAsync(object data)
        {
            if (!IsConnected || _disposed) return;

            try
            {
                lock (_sendLock)
                {
                    if (!IsConnected || _disposed) return;
                    
                    // Simulate sending data to client
                    var json = JsonSerializer.Serialize(data);
                    // In real implementation, this would send via TCP socket
                    // For simulation, we just track the send operation
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending to {ConnectionId}: {ex.Message}");
                IsConnected = false;
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            IsConnected = false;
            _disposed = true;
        }
    }
}
