# Combat System Performance Test Framework

Một framework test hiệu suất toàn diện để đánh giá và so sánh 3 phương pháp xử lý damage khác nhau trong hệ thống combat game với khả năng mô phỏng 500-1000 kết nối đồng thời.

## 🎯 Mục tiêu

Framework này được thiết kế để test và so sánh hiệu suất của 3 phương pháp xử lý damage:

1. **Individual Timer Approach**: Mỗi player có timer riêng biệt
2. **Task-based Approach**: Sử dụng async Tasks để xử lý damage
3. **Centralized Timer Approach**: Timer chung với hệ thống damage queue

## 🏗️ Kiến trúc

### Cấu trúc Projects

```
CombatTestFramework/
├── CombatTestShared/          # Models và interfaces chung
├── CombatTestServer/          # TCP Server và Combat Systems
├── CombatTestClient/          # Client Simulator
├── CombatTestPerformance/     # Performance Testing Framework
└── CombatTestConsole/         # Console Application chính
```

### Thành phần chính

- **TCP Server**: <PERSON><PERSON> lý 500-1000 kết nối đồng thời
- **Combat System**: 3 phương pháp xử lý damage khác nhau
- **Client Simulator**: Mô phỏng 500 players với attack patterns
- **Performance Monitor**: Đo lường CPU, Memory, Latency, Throughput
- **Test Framework**: So sánh và phân tích hiệu suất

## 🚀 Cách sử dụng

### 1. Build Project

```bash
dotnet build
```

### 2. Chạy Tests

```bash
cd CombatTestConsole
dotnet run
```

### 3. Menu Options

- **Quick Test**: 100 players, 30 giây
- **Standard Test**: 500 players, 2 phút  
- **High Load Test**: 1000 players, 5 phút
- **Stress Test**: 1000 players, 10 phút
- **Custom Test**: Cấu hình tùy chỉnh

## ⚙️ Cấu hình

### File cấu hình: `appsettings.json`

```json
{
  "playerCount": 500,
  "monsterCountPerPlayer": 10,
  "skillsPerPlayer": 3,
  "testDuration": "00:02:00",
  "attackInterval": "00:00:03",
  "combat": {
    "minHitsPerSkill": 1,
    "maxHitsPerSkill": 5,
    "minDamagePerHit": 50,
    "maxDamagePerHit": 200,
    "criticalChance": 0.15,
    "skillDuration": "00:00:02"
  }
}
```

### Các tham số có thể điều chỉnh

- **Player Count**: Số lượng players (1-2000)
- **Monster Count**: Số monsters mỗi player tấn công (1-50)
- **Test Duration**: Thời gian test (1-60 phút)
- **Attack Interval**: Khoảng cách giữa các đòn tấn công (1-30 giây)
- **Hit Patterns**: Số lượng hits mỗi skill (1-10)

## 📊 Metrics được đo lường

### Performance Metrics
- **CPU Usage**: Sử dụng CPU trung bình và peak
- **Memory Usage**: Sử dụng RAM trung bình và peak
- **Latency**: Độ trễ xử lý damage packets
- **Throughput**: Số packets/giây và attacks/giây
- **GC Collections**: Số lần garbage collection

### Test Results
- **Total Damage Packets**: Tổng số damage packets được gửi
- **Total Attacks**: Tổng số attacks được xử lý
- **Success Rate**: Tỷ lệ thành công
- **Execution Time**: Thời gian thực thi

## 🔬 3 Phương pháp Combat

### 1. Individual Timer Approach
- Mỗi player có timer riêng cho từng attack
- Ưu điểm: Độc lập, dễ debug
- Nhược điểm: Nhiều timers, tốn tài nguyên

### 2. Task-based Approach  
- Sử dụng async Tasks với delays
- Ưu điểm: Linh hoạt, scalable
- Nhược điểm: Task overhead, phức tạp hơn

### 3. Centralized Timer Approach
- Timer chung với damage queue system
- Ưu điểm: Hiệu quả tài nguyên, kiểm soát tốt
- Nhược điểm: Single point of failure

## 📈 Kết quả Test

Framework sẽ tạo báo cáo so sánh chi tiết:

```
PERFORMANCE COMPARISON REPORT
============================================================
Individual Timer:
  Avg CPU: 45.2% (Peak: 78.5%)
  Avg Memory: 256 MB (Peak: 384 MB)
  Avg Latency: 12.3 ms
  Throughput: 1,250 packets/sec

Task-based:
  Avg CPU: 38.7% (Peak: 65.2%)
  Avg Memory: 198 MB (Peak: 298 MB)
  Avg Latency: 8.7 ms
  Throughput: 1,580 packets/sec

Centralized Timer:
  Avg CPU: 28.9% (Peak: 52.1%)
  Avg Memory: 145 MB (Peak: 210 MB)
  Avg Latency: 5.2 ms
  Throughput: 2,100 packets/sec

BEST PERFORMERS:
CPU Efficiency: CentralizedTimer
Memory Efficiency: CentralizedTimer
Lowest Latency: CentralizedTimer
Highest Throughput: CentralizedTimer
```

## 🎮 Combat System Specs

### Player Specifications
- Mỗi player có thể tấn công nhiều targets đồng thời
- Mỗi skill có 1-5 hits với timing khác nhau
- Attack duration: 2 giây mỗi skill
- Hit timing: Có thể equal hoặc unequal spacing

### Test Scenarios
- 500 simulated players
- Mỗi player tấn công 10 monsters đồng thời
- Mỗi skill có 1-5 hits với variable timing
- Damage packets được gửi cho mỗi hit

## 📁 Output Files

- **Comparison Reports**: `comparison-report-YYYYMMDD-HHMMSS.json`
- **Log Files**: `logs/combat-test-YYYYMMDD.log`
- **Configuration**: `custom-config.json`

## 🛠️ Requirements

- .NET 9.0 SDK
- Windows/Linux/macOS
- Minimum 4GB RAM (8GB recommended cho stress tests)
- Multi-core CPU (recommended)

## 🔧 Troubleshooting

### Common Issues

1. **OutOfMemory**: Giảm player count hoặc test duration
2. **High CPU**: Kiểm tra concurrent connections limit
3. **Network errors**: Đảm bảo port 8080 available

### Performance Tips

- Chạy garbage collection trước mỗi test
- Đóng các ứng dụng khác khi chạy stress test
- Monitor system resources trong quá trình test

## 📝 Logging

Framework hỗ trợ logging chi tiết:

```csharp
Logger.Configure(enableConsole: true, enableFile: true, LogLevel.Info);
```

Log levels: Debug, Info, Warning, Error, Critical

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.
