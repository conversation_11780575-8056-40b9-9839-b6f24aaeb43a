using System;
using System.IO;
using System.Threading;

namespace CombatTestShared.Logging
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    }

    public static class Logger
    {
        private static readonly object _lock = new object();
        private static string _logDirectory = "logs";
        private static bool _enableConsoleOutput = true;
        private static bool _enableFileOutput = true;
        private static LogLevel _minimumLevel = LogLevel.Info;

        static Logger()
        {
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
        }

        public static void Configure(bool enableConsole = true, bool enableFile = true, LogLevel minimumLevel = LogLevel.Info)
        {
            _enableConsoleOutput = enableConsole;
            _enableFileOutput = enableFile;
            _minimumLevel = minimumLevel;
        }

        public static void Debug(string message, params object[] args)
        {
            Log(LogLevel.Debug, message, args);
        }

        public static void Info(string message, params object[] args)
        {
            Log(LogLevel.Info, message, args);
        }

        public static void Warning(string message, params object[] args)
        {
            Log(LogLevel.Warning, message, args);
        }

        public static void Error(string message, params object[] args)
        {
            Log(LogLevel.Error, message, args);
        }

        public static void Error(Exception ex, string message = null, params object[] args)
        {
            var fullMessage = message != null ? $"{string.Format(message, args)}: {ex}" : ex.ToString();
            Log(LogLevel.Error, fullMessage);
        }

        public static void Critical(string message, params object[] args)
        {
            Log(LogLevel.Critical, message, args);
        }

        public static void Critical(Exception ex, string message = null, params object[] args)
        {
            var fullMessage = message != null ? $"{string.Format(message, args)}: {ex}" : ex.ToString();
            Log(LogLevel.Critical, fullMessage);
        }

        private static void Log(LogLevel level, string message, params object[] args)
        {
            if (level < _minimumLevel) return;

            var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var threadId = Thread.CurrentThread.ManagedThreadId;
            var logEntry = $"[{timestamp}] [{level}] [Thread-{threadId}] {formattedMessage}";

            lock (_lock)
            {
                if (_enableConsoleOutput)
                {
                    WriteToConsole(level, logEntry);
                }

                if (_enableFileOutput)
                {
                    WriteToFile(level, logEntry);
                }
            }
        }

        private static void WriteToConsole(LogLevel level, string logEntry)
        {
            var originalColor = Console.ForegroundColor;
            
            Console.ForegroundColor = level switch
            {
                LogLevel.Debug => ConsoleColor.Gray,
                LogLevel.Info => ConsoleColor.White,
                LogLevel.Warning => ConsoleColor.Yellow,
                LogLevel.Error => ConsoleColor.Red,
                LogLevel.Critical => ConsoleColor.Magenta,
                _ => ConsoleColor.White
            };

            Console.WriteLine(logEntry);
            Console.ForegroundColor = originalColor;
        }

        private static void WriteToFile(LogLevel level, string logEntry)
        {
            try
            {
                var fileName = $"combat-test-{DateTime.UtcNow:yyyyMMdd}.log";
                var filePath = Path.Combine(_logDirectory, fileName);
                
                File.AppendAllText(filePath, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                // Fallback to console if file writing fails
                Console.WriteLine($"Failed to write to log file: {ex.Message}");
                Console.WriteLine(logEntry);
            }
        }

        public static void LogPerformanceMetric(string metricName, double value, string unit = "")
        {
            Info($"PERF: {metricName} = {value:F2} {unit}");
        }

        public static void LogTestResult(string testName, bool success, TimeSpan duration, string details = "")
        {
            var level = success ? LogLevel.Info : LogLevel.Error;
            var status = success ? "SUCCESS" : "FAILED";
            var message = $"TEST: {testName} - {status} ({duration:hh\\:mm\\:ss})";
            
            if (!string.IsNullOrEmpty(details))
            {
                message += $" - {details}";
            }
            
            Log(level, message);
        }

        public static void LogSystemEvent(string eventName, object data = null)
        {
            var message = $"SYSTEM: {eventName}";
            if (data != null)
            {
                message += $" - {data}";
            }
            Info(message);
        }
    }
}
