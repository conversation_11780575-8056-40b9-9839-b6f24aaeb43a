using System;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Text.Json;
using CombatTestShared.Models;

namespace CombatTestClient
{
    public class ClientSimulator
    {
        private readonly NetworkConfiguration _config;
        private readonly ConcurrentDictionary<int, SimulatedClient> _clients;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning;

        public int ConnectedClients => _clients.Count(kvp => kvp.Value.IsConnected);
        public bool IsRunning => _isRunning;

        public event EventHandler<string> StatusMessage;
        public event EventHandler<DamagePacket> DamageReceived;

        public ClientSimulator(NetworkConfiguration config)
        {
            _config = config ?? new NetworkConfiguration();
            _clients = new ConcurrentDictionary<int, SimulatedClient>();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task StartAsync(int clientCount)
        {
            if (_isRunning) return;

            _isRunning = true;
            StatusMessage?.Invoke(this, $"Starting {clientCount} simulated clients...");

            var connectionTasks = new List<Task>();

            for (int i = 1; i <= clientCount; i++)
            {
                var client = new SimulatedClient(i, _config);
                client.DamageReceived += OnClientDamageReceived;
                client.StatusChanged += OnClientStatusChanged;

                _clients.TryAdd(i, client);

                var connectTask = client.ConnectAsync(_cancellationTokenSource.Token);
                connectionTasks.Add(connectTask);

                // Stagger connections to avoid overwhelming the server
                if (i % 10 == 0)
                {
                    await Task.Delay(100, _cancellationTokenSource.Token);
                }
            }

            // Wait for all connections to complete
            await Task.WhenAll(connectionTasks);

            StatusMessage?.Invoke(this, $"Client simulator started with {ConnectedClients}/{clientCount} connected clients");
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            _isRunning = false;
            _cancellationTokenSource.Cancel();

            StatusMessage?.Invoke(this, "Stopping client simulator...");

            var disconnectTasks = _clients.Values.Select(client => client.DisconnectAsync());
            await Task.WhenAll(disconnectTasks);

            _clients.Clear();
            StatusMessage?.Invoke(this, "Client simulator stopped");
        }

        public async Task SimulateAttackPatternAsync(TestConfiguration testConfig)
        {
            if (!_isRunning)
                throw new InvalidOperationException("Client simulator is not running");

            StatusMessage?.Invoke(this, $"Starting attack pattern simulation for {testConfig.TestDuration}");

            var simulationTasks = new List<Task>();
            var endTime = DateTime.UtcNow.Add(testConfig.TestDuration);

            foreach (var client in _clients.Values.Where(c => c.IsConnected))
            {
                var task = SimulateClientAttacks(client, testConfig, endTime);
                simulationTasks.Add(task);
            }

            await Task.WhenAll(simulationTasks);
            StatusMessage?.Invoke(this, "Attack pattern simulation completed");
        }

        private async Task SimulateClientAttacks(SimulatedClient client, TestConfiguration config, DateTime endTime)
        {
            var random = new Random(client.PlayerId); // Seed with player ID for reproducible patterns

            while (DateTime.UtcNow < endTime && _isRunning && client.IsConnected)
            {
                try
                {
                    // Generate attack command
                    var skillId = random.Next(1, config.SkillsPerPlayer + 1);
                    var targetIds = GenerateTargetIds(client.PlayerId, config.MonsterCountPerPlayer, random);

                    var attackCommand = new AttackCommand(client.PlayerId, skillId, targetIds);

                    // Send attack command to server
                    await client.SendAttackCommandAsync(attackCommand);

                    // Wait for next attack
                    var jitter = random.Next(-500, 500); // Add some randomness
                    var delay = config.AttackInterval.Add(TimeSpan.FromMilliseconds(jitter));
                    await Task.Delay(delay, _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    StatusMessage?.Invoke(this, $"Error in client {client.PlayerId} attack simulation: {ex.Message}");
                    await Task.Delay(1000); // Wait before retrying
                }
            }
        }

        private int[] GenerateTargetIds(int playerId, int targetCount, Random random)
        {
            var targets = new int[targetCount];
            var baseMonsterIndex = (playerId - 1) * targetCount;

            for (int i = 0; i < targetCount; i++)
            {
                targets[i] = baseMonsterIndex + i + 1;
            }

            return targets;
        }

        private void OnClientDamageReceived(object sender, DamagePacket damage)
        {
            DamageReceived?.Invoke(this, damage);
        }

        private void OnClientStatusChanged(object sender, string status)
        {
            // Optionally log individual client status changes
            // StatusMessage?.Invoke(this, status);
        }

        public ClientStatistics GetStatistics()
        {
            var stats = new ClientStatistics
            {
                TotalClients = _clients.Count,
                ConnectedClients = ConnectedClients,
                DisconnectedClients = _clients.Count - ConnectedClients
            };

            foreach (var client in _clients.Values)
            {
                stats.TotalAttacksSent += client.AttacksSent;
                stats.TotalDamageReceived += client.DamagePacketsReceived;
                stats.TotalBytesReceived += client.BytesReceived;
                stats.TotalBytesSent += client.BytesSent;
            }

            return stats;
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _cancellationTokenSource?.Dispose();
        }
    }

    public class ClientStatistics
    {
        public int TotalClients { get; set; }
        public int ConnectedClients { get; set; }
        public int DisconnectedClients { get; set; }
        public long TotalAttacksSent { get; set; }
        public long TotalDamageReceived { get; set; }
        public long TotalBytesReceived { get; set; }
        public long TotalBytesSent { get; set; }
        public double ConnectionSuccessRate => TotalClients > 0 ? (double)ConnectedClients / TotalClients * 100 : 0;
    }
}
