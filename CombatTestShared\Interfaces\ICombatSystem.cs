using System;
using System.Threading.Tasks;
using CombatTestShared.Models;

namespace CombatTestShared.Interfaces
{
    public interface ICombatSystem
    {
        string Name { get; }
        AttackApproach Approach { get; }
        bool IsRunning { get; }
        
        Task StartAsync();
        Task StopAsync();
        Task<DamageResult> ExecuteAttackAsync(AttackCommand command);
        
        event EventHandler<DamagePacket> DamageDealt;
        event EventHandler<string> SystemMessage;
    }

    public interface IDamageProcessor
    {
        Task ProcessDamageAsync(DamagePacket packet);
        Task ProcessBatchDamageAsync(DamagePacket[] packets);
    }

    public interface IPerformanceMonitor
    {
        void StartMonitoring();
        void StopMonitoring();
        PerformanceMetrics GetCurrentMetrics();
        PerformanceReport GenerateReport(TimeSpan duration);
        
        void RecordDamagePacket(DamagePacket packet);
        void RecordAttackCommand(AttackCommand command);
        void RecordSystemEvent(string eventName, object data);
    }

    public interface IConnectionManager
    {
        Task<bool> AcceptConnectionAsync(string connectionId);
        Task DisconnectAsync(string connectionId);
        Task SendToClientAsync(string connectionId, object data);
        Task BroadcastAsync(object data);
        
        int ActiveConnections { get; }
        event EventHandler<string> ClientConnected;
        event EventHandler<string> ClientDisconnected;
    }

    public interface ITestScenario
    {
        string Name { get; }
        TestConfiguration Configuration { get; }
        
        Task<TestResult> ExecuteAsync(ICombatSystem combatSystem);
        Task<TestResult> ExecuteAsync(ICombatSystem combatSystem, IPerformanceMonitor monitor);
    }
}
