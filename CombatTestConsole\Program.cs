﻿using System;
using System.Threading.Tasks;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;
using CombatTestServer.Network;
using CombatTestServer.Combat;
using CombatTestClient;
using CombatTestPerformance;
using CombatTestPerformance.TestScenarios;

namespace CombatTestConsole
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("COMBAT SYSTEM PERFORMANCE TEST FRAMEWORK");
            Console.WriteLine(new string('=', 60));
            Console.WriteLine("Testing 3 approaches: Individual Timer, Task-based, Centralized Timer");
            Console.WriteLine();

            try
            {
                var config = LoadOrCreateConfiguration();

                while (true)
                {
                    ShowMainMenu();
                    var choice = Console.ReadLine();

                    switch (choice?.ToLower())
                    {
                        case "1":
                            await RunQuickTest(config);
                            break;
                        case "2":
                            await RunStandardTest(config);
                            break;
                        case "3":
                            await RunHighLoadTest(config);
                            break;
                        case "4":
                            await RunStressTest(config);
                            break;
                        case "5":
                            await RunCustomTest(config);
                            break;
                        case "6":
                            config = ModifyConfiguration(config);
                            break;
                        case "7":
                            ShowConfiguration(config);
                            break;
                        case "8":
                            await RunSingleApproachTest(config);
                            break;
                        case "9":
                            await RunComparisonTest(config);
                            break;
                        case "q":
                        case "quit":
                        case "exit":
                            Console.WriteLine("Exiting...");
                            return;
                        default:
                            Console.WriteLine("Invalid choice. Please try again.");
                            break;
                    }

                    Console.WriteLine("\nPress any key to continue...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fatal error: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        static void ShowMainMenu()
        {
            Console.WriteLine("MAIN MENU:");
            Console.WriteLine("1. Quick Test (100 players, 30 seconds)");
            Console.WriteLine("2. Standard Test (500 players, 2 minutes)");
            Console.WriteLine("3. High Load Test (1000 players, 5 minutes)");
            Console.WriteLine("4. Stress Test (1000 players, 10 minutes)");
            Console.WriteLine("5. Custom Test");
            Console.WriteLine("6. Modify Configuration");
            Console.WriteLine("7. Show Current Configuration");
            Console.WriteLine("8. Test Single Approach");
            Console.WriteLine("9. Run Full Comparison");
            Console.WriteLine("Q. Quit");
            Console.WriteLine();
            Console.Write("Choose an option: ");
        }

        static TestConfiguration LoadOrCreateConfiguration()
        {
            var config = ConfigurationManager.LoadConfiguration();
            ConfigurationManager.PrintConfiguration(config);
            return config;
        }

        static TestConfiguration ModifyConfiguration(TestConfiguration config)
        {
            var modifiedConfig = ConfigurationManager.ModifyConfiguration(config);
            ConfigurationManager.SaveConfiguration(modifiedConfig);
            return modifiedConfig;
        }

        static void ShowConfiguration(TestConfiguration config)
        {
            ConfigurationManager.PrintConfiguration(config);
        }

        static async Task RunQuickTest(TestConfiguration config)
        {
            Console.WriteLine("Running Quick Test...");
            var quickConfig = ConfigurationManager.CreateQuickTestConfiguration();
            await RunFullComparison(quickConfig);
        }

        static async Task RunStandardTest(TestConfiguration config)
        {
            Console.WriteLine("Running Standard Test...");
            await RunFullComparison(config);
        }

        static async Task RunHighLoadTest(TestConfiguration config)
        {
            Console.WriteLine("Running High Load Test...");
            var highLoadConfig = ConfigurationManager.CreateHighLoadConfiguration();
            await RunFullComparison(highLoadConfig);
        }

        static async Task RunStressTest(TestConfiguration config)
        {
            Console.WriteLine("Running Stress Test...");
            var stressConfig = ConfigurationManager.CreateStressTestConfiguration();
            await RunFullComparison(stressConfig);
        }

        static async Task RunCustomTest(TestConfiguration config)
        {
            Console.WriteLine("Running Custom Test...");
            var customConfig = ConfigurationManager.ModifyConfiguration(config);
            await RunFullComparison(customConfig);
        }

        static async Task RunSingleApproachTest(TestConfiguration config)
        {
            Console.WriteLine("Select approach to test:");
            Console.WriteLine("1. Individual Timer");
            Console.WriteLine("2. Task-based");
            Console.WriteLine("3. Centralized Timer");
            Console.Write("Choice: ");

            var choice = Console.ReadLine();
            AttackApproach approach;

            switch (choice)
            {
                case "1":
                    approach = AttackApproach.IndividualTimer;
                    break;
                case "2":
                    approach = AttackApproach.TaskBased;
                    break;
                case "3":
                    approach = AttackApproach.CentralizedTimer;
                    break;
                default:
                    Console.WriteLine("Invalid choice. Using Individual Timer.");
                    approach = AttackApproach.IndividualTimer;
                    break;
            }

            await RunSingleApproach(config, approach);
        }

        static async Task RunComparisonTest(TestConfiguration config)
        {
            Console.WriteLine("Running Full Comparison Test...");
            await RunFullComparison(config);
        }

        static async Task RunSingleApproach(TestConfiguration config, AttackApproach approach)
        {
            Console.WriteLine($"Testing {approach} approach...");
            Console.WriteLine(new string('-', 50));

            try
            {
                // Create combat system based on approach
                var combatSystem = CreateCombatSystem(approach, config);
                var scenario = TestScenarioFactory.CreateStandardLoadTest();

                // Run the test
                var result = await scenario.ExecuteAsync(combatSystem);

                // Display results
                PrintSingleTestResult(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running {approach} test: {ex.Message}");
            }
        }

        static async Task RunFullComparison(TestConfiguration config)
        {
            Console.WriteLine("Starting comprehensive comparison test...");
            Console.WriteLine(new string('=', 60));

            var testRunner = new TestRunner();

            // Add all combat systems
            testRunner.AddCombatSystem(CreateCombatSystem(AttackApproach.IndividualTimer, config));
            testRunner.AddCombatSystem(CreateCombatSystem(AttackApproach.TaskBased, config));
            testRunner.AddCombatSystem(CreateCombatSystem(AttackApproach.CentralizedTimer, config));

            // Add test scenarios
            testRunner.AddTestScenario(TestScenarioFactory.CreateCustomTest(config));

            try
            {
                // Run all tests
                var comparisonReport = await testRunner.RunAllTestsAsync();

                // Display comprehensive results
                testRunner.PrintComparisonReport(comparisonReport);

                // Save results to file
                SaveComparisonReport(comparisonReport);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running comparison test: {ex.Message}");
            }
        }

        static ICombatSystem CreateCombatSystem(AttackApproach approach, TestConfiguration config)
        {
            var connectionManager = new ConnectionManager(config.Network);
            var damageProcessor = new DamageProcessor(connectionManager);

            return approach switch
            {
                AttackApproach.IndividualTimer => new IndividualTimerCombatSystem(damageProcessor, config.Combat),
                AttackApproach.TaskBased => new TaskBasedCombatSystem(damageProcessor, config.Combat),
                AttackApproach.CentralizedTimer => new CentralizedTimerCombatSystem(damageProcessor, config.Combat),
                _ => throw new ArgumentException($"Unknown approach: {approach}")
            };
        }

        static void PrintSingleTestResult(TestResult result)
        {
            Console.WriteLine($"Test: {result.TestName}");
            Console.WriteLine($"Approach: {result.Approach}");
            Console.WriteLine($"Success: {result.Success}");
            Console.WriteLine($"Duration: {result.Duration:hh\\:mm\\:ss}");

            if (result.Success && result.PerformanceReport != null)
            {
                var report = result.PerformanceReport;
                Console.WriteLine($"Total Damage Packets: {report.TotalDamagePackets:N0}");
                Console.WriteLine($"Total Attacks: {report.TotalAttacks:N0}");
                Console.WriteLine($"Packets/sec: {report.PacketsPerSecond:F2}");
                Console.WriteLine($"Attacks/sec: {report.AttacksPerSecond:F2}");

                if (report.AverageMetrics != null)
                {
                    Console.WriteLine($"Avg CPU: {report.AverageMetrics.CpuUsagePercent:F2}%");
                    Console.WriteLine($"Avg Memory: {report.AverageMetrics.MemoryUsageMB:N0} MB");
                    Console.WriteLine($"Avg Latency: {report.AverageMetrics.AverageLatencyMs:F2} ms");
                }
            }

            if (!result.Success)
            {
                Console.WriteLine($"Error: {result.ErrorMessage}");
            }
        }

        static void SaveComparisonReport(ComparisonReport report)
        {
            try
            {
                var fileName = $"comparison-report-{DateTime.Now:yyyyMMdd-HHmmss}.json";
                var json = System.Text.Json.JsonSerializer.Serialize(report, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                System.IO.File.WriteAllText(fileName, json);
                Console.WriteLine($"Comparison report saved to: {fileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving comparison report: {ex.Message}");
            }
        }
    }
}
