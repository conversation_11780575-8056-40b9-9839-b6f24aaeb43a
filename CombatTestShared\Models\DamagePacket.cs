using System;

namespace CombatTestShared.Models
{
    public class DamagePacket
    {
        public string Id { get; set; }
        public int PlayerId { get; set; }
        public int MonsterId { get; set; }
        public int SkillId { get; set; }
        public int HitIndex { get; set; }
        public int Damage { get; set; }
        public DamageType Type { get; set; }
        public bool IsCritical { get; set; }
        public DateTime Timestamp { get; set; }
        public TimeSpan ProcessingDelay { get; set; }

        public DamagePacket()
        {
            Id = Guid.NewGuid().ToString();
            Timestamp = DateTime.UtcNow;
            ProcessingDelay = TimeSpan.Zero;
        }

        public DamagePacket(int playerId, int monsterId, int skillId, int hitIndex, int damage) : this()
        {
            PlayerId = playerId;
            MonsterId = monsterId;
            SkillId = skillId;
            HitIndex = hitIndex;
            Damage = damage;
            Type = DamageType.Physical;
        }
    }

    public class AttackCommand
    {
        public string Id { get; set; }
        public int PlayerId { get; set; }
        public int SkillId { get; set; }
        public int[] TargetMonsterIds { get; set; }
        public DateTime StartTime { get; set; }
        public AttackApproach Approach { get; set; }

        public AttackCommand()
        {
            Id = Guid.NewGuid().ToString();
            StartTime = DateTime.UtcNow;
            TargetMonsterIds = new int[0];
        }

        public AttackCommand(int playerId, int skillId, int[] targetIds) : this()
        {
            PlayerId = playerId;
            SkillId = skillId;
            TargetMonsterIds = targetIds;
        }
    }

    public class DamageResult
    {
        public string AttackId { get; set; }
        public int TotalDamageDealt { get; set; }
        public int MonstersKilled { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime CompletedAt { get; set; }
        public DamagePacket[] DamagePackets { get; set; }

        public DamageResult()
        {
            CompletedAt = DateTime.UtcNow;
            DamagePackets = new DamagePacket[0];
        }
    }

    public enum DamageType
    {
        Physical,
        Magical,
        True
    }

    public enum AttackApproach
    {
        IndividualTimer,
        TaskBased,
        CentralizedTimer
    }
}
