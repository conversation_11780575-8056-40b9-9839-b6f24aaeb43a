using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestPerformance
{
    public class TestRunner
    {
        private readonly List<ICombatSystem> _combatSystems;
        private readonly List<ITestScenario> _testScenarios;
        private readonly List<TestResult> _testResults;

        public IReadOnlyList<TestResult> TestResults => _testResults.AsReadOnly();

        public TestRunner()
        {
            _combatSystems = new List<ICombatSystem>();
            _testScenarios = new List<ITestScenario>();
            _testResults = new List<TestResult>();
        }

        public void AddCombatSystem(ICombatSystem combatSystem)
        {
            if (combatSystem == null)
                throw new ArgumentNullException(nameof(combatSystem));
            
            _combatSystems.Add(combatSystem);
        }

        public void AddTestScenario(ITestScenario testScenario)
        {
            if (testScenario == null)
                throw new ArgumentNullException(nameof(testScenario));
            
            _testScenarios.Add(testScenario);
        }

        public async Task<ComparisonReport> RunAllTestsAsync()
        {
            Console.WriteLine("Starting comprehensive combat system performance tests...");
            Console.WriteLine($"Combat Systems: {_combatSystems.Count}");
            Console.WriteLine($"Test Scenarios: {_testScenarios.Count}");
            Console.WriteLine($"Total Tests: {_combatSystems.Count * _testScenarios.Count}");
            Console.WriteLine();

            _testResults.Clear();
            var startTime = DateTime.UtcNow;

            foreach (var scenario in _testScenarios)
            {
                Console.WriteLine($"Running scenario: {scenario.Name}");
                Console.WriteLine(new string('=', 50));

                foreach (var combatSystem in _combatSystems)
                {
                    Console.WriteLine($"Testing {combatSystem.Name}...");
                    
                    try
                    {
                        // Run garbage collection before each test for fair comparison
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();
                        
                        await Task.Delay(1000); // Brief pause between tests
                        
                        var result = await scenario.ExecuteAsync(combatSystem);
                        _testResults.Add(result);
                        
                        PrintTestResult(result);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Test failed: {ex.Message}");
                        var failedResult = new TestResult
                        {
                            TestName = scenario.Name,
                            Approach = combatSystem.Approach,
                            StartTime = DateTime.UtcNow,
                            EndTime = DateTime.UtcNow,
                            Success = false,
                            ErrorMessage = ex.Message
                        };
                        _testResults.Add(failedResult);
                    }
                }
                
                Console.WriteLine();
            }

            var endTime = DateTime.UtcNow;
            var totalDuration = endTime - startTime;

            Console.WriteLine("All tests completed!");
            Console.WriteLine($"Total execution time: {totalDuration:hh\\:mm\\:ss}");
            Console.WriteLine();

            return GenerateComparisonReport(totalDuration);
        }

        public async Task<TestResult> RunSingleTestAsync(ICombatSystem combatSystem, ITestScenario scenario)
        {
            Console.WriteLine($"Running single test: {scenario.Name} with {combatSystem.Name}");
            
            // Run garbage collection before test
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var result = await scenario.ExecuteAsync(combatSystem);
            PrintTestResult(result);
            
            return result;
        }

        private void PrintTestResult(TestResult result)
        {
            Console.WriteLine($"  Result: {(result.Success ? "SUCCESS" : "FAILED")}");
            Console.WriteLine($"  Duration: {result.Duration:hh\\:mm\\:ss}");
            
            if (result.Success && result.PerformanceReport != null)
            {
                var report = result.PerformanceReport;
                Console.WriteLine($"  Avg CPU: {report.AverageMetrics?.CpuUsagePercent:F2}%");
                Console.WriteLine($"  Peak CPU: {report.PeakMetrics?.CpuUsagePercent:F2}%");
                Console.WriteLine($"  Avg Memory: {report.AverageMetrics?.MemoryUsageMB:N0} MB");
                Console.WriteLine($"  Peak Memory: {report.PeakMetrics?.MemoryUsageMB:N0} MB");
                Console.WriteLine($"  Total Damage Packets: {report.TotalDamagePackets:N0}");
                Console.WriteLine($"  Packets/sec: {report.PacketsPerSecond:F2}");
                Console.WriteLine($"  Total Attacks: {report.TotalAttacks:N0}");
                Console.WriteLine($"  Attacks/sec: {report.AttacksPerSecond:F2}");
                Console.WriteLine($"  Avg Latency: {report.AverageMetrics?.AverageLatencyMs:F2} ms");
            }
            
            if (!result.Success)
            {
                Console.WriteLine($"  Error: {result.ErrorMessage}");
            }
            
            Console.WriteLine();
        }

        private ComparisonReport GenerateComparisonReport(TimeSpan totalDuration)
        {
            var report = new ComparisonReport
            {
                GeneratedAt = DateTime.UtcNow,
                TotalTestDuration = totalDuration,
                TotalTests = _testResults.Count,
                SuccessfulTests = _testResults.Count(r => r.Success),
                FailedTests = _testResults.Count(r => !r.Success)
            };

            // Group results by approach
            var resultsByApproach = _testResults
                .Where(r => r.Success)
                .GroupBy(r => r.Approach)
                .ToList();

            foreach (var group in resultsByApproach)
            {
                var approach = group.Key;
                var results = group.ToList();
                
                var comparison = new ApproachComparison
                {
                    Approach = approach,
                    TestCount = results.Count,
                    AverageDuration = TimeSpan.FromTicks((long)results.Average(r => r.Duration.Ticks)),
                    TotalDamagePackets = results.Sum(r => r.PerformanceReport?.TotalDamagePackets ?? 0),
                    TotalAttacks = results.Sum(r => r.PerformanceReport?.TotalAttacks ?? 0),
                    AverageCpuUsage = results.Average(r => r.PerformanceReport?.AverageMetrics?.CpuUsagePercent ?? 0),
                    PeakCpuUsage = results.Max(r => r.PerformanceReport?.PeakMetrics?.CpuUsagePercent ?? 0),
                    AverageMemoryUsage = (long)results.Average(r => r.PerformanceReport?.AverageMetrics?.MemoryUsageMB ?? 0),
                    PeakMemoryUsage = results.Max(r => r.PerformanceReport?.PeakMetrics?.MemoryUsageMB ?? 0),
                    AverageLatency = results.Average(r => r.PerformanceReport?.AverageMetrics?.AverageLatencyMs ?? 0),
                    AverageThroughput = results.Average(r => r.PerformanceReport?.PacketsPerSecond ?? 0)
                };
                
                report.ApproachComparisons.Add(comparison);
            }

            // Determine best approach for each metric
            if (report.ApproachComparisons.Count > 0)
            {
                report.BestCpuEfficiency = report.ApproachComparisons.OrderBy(c => c.AverageCpuUsage).First().Approach;
                report.BestMemoryEfficiency = report.ApproachComparisons.OrderBy(c => c.AverageMemoryUsage).First().Approach;
                report.BestLatency = report.ApproachComparisons.OrderBy(c => c.AverageLatency).First().Approach;
                report.BestThroughput = report.ApproachComparisons.OrderByDescending(c => c.AverageThroughput).First().Approach;
            }

            return report;
        }

        public void PrintComparisonReport(ComparisonReport report)
        {
            Console.WriteLine("PERFORMANCE COMPARISON REPORT");
            Console.WriteLine(new string('=', 60));
            Console.WriteLine($"Generated: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"Total Test Duration: {report.TotalTestDuration:hh\\:mm\\:ss}");
            Console.WriteLine($"Tests: {report.SuccessfulTests}/{report.TotalTests} successful");
            Console.WriteLine();

            Console.WriteLine("APPROACH COMPARISON:");
            Console.WriteLine(new string('-', 60));
            
            foreach (var comparison in report.ApproachComparisons)
            {
                Console.WriteLine($"{comparison.Approach}:");
                Console.WriteLine($"  Tests: {comparison.TestCount}");
                Console.WriteLine($"  Avg Duration: {comparison.AverageDuration:hh\\:mm\\:ss}");
                Console.WriteLine($"  Total Damage Packets: {comparison.TotalDamagePackets:N0}");
                Console.WriteLine($"  Total Attacks: {comparison.TotalAttacks:N0}");
                Console.WriteLine($"  Avg CPU: {comparison.AverageCpuUsage:F2}% (Peak: {comparison.PeakCpuUsage:F2}%)");
                Console.WriteLine($"  Avg Memory: {comparison.AverageMemoryUsage:N0} MB (Peak: {comparison.PeakMemoryUsage:N0} MB)");
                Console.WriteLine($"  Avg Latency: {comparison.AverageLatency:F2} ms");
                Console.WriteLine($"  Avg Throughput: {comparison.AverageThroughput:F2} packets/sec");
                Console.WriteLine();
            }

            Console.WriteLine("BEST PERFORMERS:");
            Console.WriteLine(new string('-', 60));
            Console.WriteLine($"CPU Efficiency: {report.BestCpuEfficiency}");
            Console.WriteLine($"Memory Efficiency: {report.BestMemoryEfficiency}");
            Console.WriteLine($"Lowest Latency: {report.BestLatency}");
            Console.WriteLine($"Highest Throughput: {report.BestThroughput}");
        }
    }
}
