using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestServer.Combat
{
    public class IndividualTimerCombatSystem : ICombatSystem
    {
        public string Name => "Individual Timer Approach";
        public AttackApproach Approach => AttackApproach.IndividualTimer;
        public bool IsRunning { get; private set; }

        private readonly ConcurrentDictionary<string, AttackExecution> _activeAttacks;
        private readonly IDamageProcessor _damageProcessor;
        private readonly CombatConfiguration _config;

        public event EventHandler<DamagePacket> DamageDealt;
        public event EventHandler<string> SystemMessage;

        public IndividualTimerCombatSystem(IDamageProcessor damageProcessor, CombatConfiguration config)
        {
            _activeAttacks = new ConcurrentDictionary<string, AttackExecution>();
            _damageProcessor = damageProcessor ?? throw new ArgumentNullException(nameof(damageProcessor));
            _config = config ?? new CombatConfiguration();
        }

        public Task StartAsync()
        {
            IsRunning = true;
            SystemMessage?.Invoke(this, $"{Name} started");
            return Task.CompletedTask;
        }

        public Task StopAsync()
        {
            IsRunning = false;
            
            // Stop all active attacks
            foreach (var attack in _activeAttacks.Values)
            {
                attack.Stop();
            }
            _activeAttacks.Clear();
            
            SystemMessage?.Invoke(this, $"{Name} stopped");
            return Task.CompletedTask;
        }

        public async Task<DamageResult> ExecuteAttackAsync(AttackCommand command)
        {
            if (!IsRunning)
                throw new InvalidOperationException("Combat system is not running");

            var skill = GenerateSkill(command.SkillId);
            var attackExecution = new AttackExecution(command, skill, this);
            
            if (_activeAttacks.TryAdd(command.Id, attackExecution))
            {
                var result = await attackExecution.ExecuteAsync();
                _activeAttacks.TryRemove(command.Id, out _);
                return result;
            }

            throw new InvalidOperationException($"Attack {command.Id} is already being executed");
        }

        private Skill GenerateSkill(int skillId)
        {
            var random = new Random();
            var hitCount = random.Next(_config.MinHitsPerSkill, _config.MaxHitsPerSkill + 1);
            var baseDamage = random.Next(_config.MinDamagePerHit, _config.MaxDamagePerHit + 1);
            
            return new Skill(skillId, $"Skill_{skillId}", baseDamage, hitCount);
        }

        internal async Task ProcessDamageAsync(DamagePacket packet)
        {
            await _damageProcessor.ProcessDamageAsync(packet);
            DamageDealt?.Invoke(this, packet);
        }

        private class AttackExecution
        {
            private readonly AttackCommand _command;
            private readonly Skill _skill;
            private readonly IndividualTimerCombatSystem _combatSystem;
            private readonly List<System.Timers.Timer> _hitTimers;
            private readonly List<DamagePacket> _damagePackets;
            private readonly TaskCompletionSource<DamageResult> _completionSource;
            private int _completedHits;

            public AttackExecution(AttackCommand command, Skill skill, IndividualTimerCombatSystem combatSystem)
            {
                _command = command;
                _skill = skill;
                _combatSystem = combatSystem;
                _hitTimers = new List<System.Timers.Timer>();
                _damagePackets = new List<DamagePacket>();
                _completionSource = new TaskCompletionSource<DamageResult>();
                _completedHits = 0;
            }

            public async Task<DamageResult> ExecuteAsync()
            {
                try
                {
                    // Create individual timers for each hit
                    for (int hitIndex = 0; hitIndex < _skill.HitCount; hitIndex++)
                    {
                        var hitTiming = _skill.HitTimings[hitIndex];
                        var timer = new System.Timers.Timer(hitTiming.DelayFromStart.TotalMilliseconds);
                        timer.AutoReset = false;
                        timer.Elapsed += async (sender, e) => await OnHitTimer(hitIndex, hitTiming);
                        
                        _hitTimers.Add(timer);
                        timer.Start();
                    }

                    // Wait for all hits to complete or timeout
                    var timeoutTask = Task.Delay(_skill.Duration.Add(TimeSpan.FromSeconds(1)));
                    var completedTask = await Task.WhenAny(_completionSource.Task, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        throw new TimeoutException($"Attack {_command.Id} timed out");
                    }

                    return await _completionSource.Task;
                }
                catch (Exception ex)
                {
                    _completionSource.TrySetException(ex);
                    throw;
                }
                finally
                {
                    Stop();
                }
            }

            private async Task OnHitTimer(int hitIndex, HitTiming hitTiming)
            {
                try
                {
                    // Process damage for all targets
                    foreach (var monsterId in _command.TargetMonsterIds)
                    {
                        var damage = CalculateDamage(_skill.BaseDamage, hitTiming.DamageMultiplier);
                        var packet = new DamagePacket(_command.PlayerId, monsterId, _skill.Id, hitIndex, damage)
                        {
                            ProcessingDelay = DateTime.UtcNow - _command.StartTime
                        };

                        _damagePackets.Add(packet);
                        await _combatSystem.ProcessDamageAsync(packet);
                    }

                    // Check if all hits completed
                    if (Interlocked.Increment(ref _completedHits) >= _skill.HitCount)
                    {
                        var result = new DamageResult
                        {
                            AttackId = _command.Id,
                            TotalDamageDealt = _damagePackets.Sum(p => p.Damage),
                            ExecutionTime = DateTime.UtcNow - _command.StartTime,
                            DamagePackets = _damagePackets.ToArray()
                        };

                        _completionSource.TrySetResult(result);
                    }
                }
                catch (Exception ex)
                {
                    _completionSource.TrySetException(ex);
                }
            }

            private int CalculateDamage(int baseDamage, float multiplier)
            {
                var random = new Random();
                var damage = (int)(baseDamage * multiplier);
                
                // Apply critical hit chance
                if (random.NextDouble() < _combatSystem._config.CriticalChance)
                {
                    damage = (int)(damage * _combatSystem._config.CriticalMultiplier);
                }

                return damage;
            }

            public void Stop()
            {
                foreach (var timer in _hitTimers)
                {
                    timer?.Stop();
                    timer?.Dispose();
                }
                _hitTimers.Clear();
            }
        }
    }
}
