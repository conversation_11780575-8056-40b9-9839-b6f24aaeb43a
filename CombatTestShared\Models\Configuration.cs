using System;
using System.Collections.Generic;

namespace CombatTestShared.Models
{
    public class TestConfiguration
    {
        public int PlayerCount { get; set; } = 500;
        public int MonsterCountPerPlayer { get; set; } = 10;
        public int SkillsPerPlayer { get; set; } = 3;
        public TimeSpan TestDuration { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan AttackInterval { get; set; } = TimeSpan.FromSeconds(3);
        public bool EnablePerformanceMonitoring { get; set; } = true;
        public bool EnableDetailedLogging { get; set; } = false;
        public AttackApproach[] ApproachesToTest { get; set; }
        public NetworkConfiguration Network { get; set; }
        public CombatConfiguration Combat { get; set; }

        public TestConfiguration()
        {
            ApproachesToTest = new[] 
            { 
                AttackApproach.IndividualTimer, 
                AttackApproach.TaskBased, 
                AttackApproach.CentralizedTimer 
            };
            Network = new NetworkConfiguration();
            Combat = new CombatConfiguration();
        }
    }

    public class NetworkConfiguration
    {
        public string ServerHost { get; set; } = "localhost";
        public int ServerPort { get; set; } = 8080;
        public int MaxConnections { get; set; } = 1000;
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public int BufferSize { get; set; } = 4096;
        public bool UseNagleAlgorithm { get; set; } = false;
    }

    public class CombatConfiguration
    {
        public int MinHitsPerSkill { get; set; } = 1;
        public int MaxHitsPerSkill { get; set; } = 5;
        public int MinDamagePerHit { get; set; } = 50;
        public int MaxDamagePerHit { get; set; } = 200;
        public float CriticalChance { get; set; } = 0.15f;
        public float CriticalMultiplier { get; set; } = 2.0f;
        public TimeSpan SkillDuration { get; set; } = TimeSpan.FromSeconds(2);
        public bool RandomizeHitTimings { get; set; } = true;
    }

    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public int ActiveConnections { get; set; }
        public long TotalDamagePacketsSent { get; set; }
        public long TotalAttacksProcessed { get; set; }
        public double AverageLatencyMs { get; set; }
        public double ThroughputPacketsPerSecond { get; set; }
        public long GCCollections { get; set; }
        public TimeSpan Uptime { get; set; }

        public PerformanceMetrics()
        {
            Timestamp = DateTime.UtcNow;
        }
    }

    public class TestResult
    {
        public string TestName { get; set; }
        public AttackApproach Approach { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public PerformanceReport PerformanceReport { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; }

        public TestResult()
        {
            AdditionalData = new Dictionary<string, object>();
        }
    }

    public class PerformanceReport
    {
        public AttackApproach Approach { get; set; }
        public PerformanceMetrics AverageMetrics { get; set; }
        public PerformanceMetrics PeakMetrics { get; set; }
        public List<PerformanceMetrics> DetailedMetrics { get; set; }
        public long TotalDamagePackets { get; set; }
        public long TotalAttacks { get; set; }
        public double PacketsPerSecond { get; set; }
        public double AttacksPerSecond { get; set; }
        public TimeSpan TestDuration { get; set; }

        public PerformanceReport()
        {
            DetailedMetrics = new List<PerformanceMetrics>();
        }
    }
}
