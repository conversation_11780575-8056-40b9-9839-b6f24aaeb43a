using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Text.Json;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestServer.Network
{
    public class TcpGameServer
    {
        private readonly NetworkConfiguration _config;
        private readonly IConnectionManager _connectionManager;
        private TcpListener _listener;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning;

        public bool IsRunning => _isRunning;
        public int ActiveConnections => _connectionManager.ActiveConnections;

        public event EventHandler<string> ServerMessage;

        public TcpGameServer(NetworkConfiguration config, IConnectionManager connectionManager)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        }

        public async Task StartAsync()
        {
            if (_isRunning)
                return;

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                _listener = new TcpListener(IPAddress.Parse(_config.ServerHost), _config.ServerPort);
                _listener.Start();
                _isRunning = true;

                var message = $"TCP Server started on {_config.ServerHost}:{_config.ServerPort}";
                Console.WriteLine(message);
                ServerMessage?.Invoke(this, message);

                // Start accepting connections
                _ = Task.Run(AcceptConnectionsAsync, _cancellationTokenSource.Token);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to start TCP Server: {ex.Message}";
                Console.WriteLine(errorMessage);
                ServerMessage?.Invoke(this, errorMessage);
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning)
                return;

            try
            {
                _isRunning = false;
                _cancellationTokenSource?.Cancel();
                _listener?.Stop();

                var message = "TCP Server stopped";
                Console.WriteLine(message);
                ServerMessage?.Invoke(this, message);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error stopping TCP Server: {ex.Message}";
                Console.WriteLine(errorMessage);
                ServerMessage?.Invoke(this, errorMessage);
            }
        }

        private async Task AcceptConnectionsAsync()
        {
            var connectionCounter = 0;

            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var tcpClient = await _listener.AcceptTcpClientAsync();
                    var connectionId = $"Client_{++connectionCounter}_{DateTime.UtcNow.Ticks}";

                    // Configure client socket
                    tcpClient.NoDelay = !_config.UseNagleAlgorithm;
                    tcpClient.ReceiveTimeout = (int)_config.ConnectionTimeout.TotalMilliseconds;
                    tcpClient.SendTimeout = (int)_config.ConnectionTimeout.TotalMilliseconds;

                    // Accept the connection
                    var accepted = await _connectionManager.AcceptConnectionAsync(connectionId);
                    
                    if (accepted)
                    {
                        // Handle client in background
                        _ = Task.Run(() => HandleClientAsync(tcpClient, connectionId), 
                                   _cancellationTokenSource.Token);
                    }
                    else
                    {
                        // Connection limit reached or server stopping
                        tcpClient.Close();
                        Console.WriteLine($"Connection rejected: {connectionId} (Limit reached or server stopping)");
                    }
                }
                catch (ObjectDisposedException)
                {
                    // Server is stopping
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        Console.WriteLine($"Error accepting connection: {ex.Message}");
                        await Task.Delay(100); // Brief delay before retrying
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient tcpClient, string connectionId)
        {
            var buffer = new byte[_config.BufferSize];
            var stream = tcpClient.GetStream();

            try
            {
                while (_isRunning && tcpClient.Connected && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _cancellationTokenSource.Token);
                    
                    if (bytesRead == 0)
                    {
                        // Client disconnected
                        break;
                    }

                    // Process received data
                    var receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    await ProcessClientMessage(connectionId, receivedData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error handling client {connectionId}: {ex.Message}");
            }
            finally
            {
                tcpClient.Close();
                await _connectionManager.DisconnectAsync(connectionId);
            }
        }

        private async Task ProcessClientMessage(string connectionId, string message)
        {
            try
            {
                // Parse and handle different message types
                // For now, just echo back or handle as needed
                Console.WriteLine($"Received from {connectionId}: {message}");
                
                // Send acknowledgment
                var response = new { Type = "Ack", Timestamp = DateTime.UtcNow };
                await _connectionManager.SendToClientAsync(connectionId, response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing message from {connectionId}: {ex.Message}");
            }
        }
    }
}
