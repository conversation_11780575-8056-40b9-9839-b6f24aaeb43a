using System;
using System.Collections.Generic;

namespace CombatTestShared.Models
{
    public class Player
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Health { get; set; }
        public int MaxHealth { get; set; }
        public PlayerPosition Position { get; set; }
        public List<Skill> Skills { get; set; }
        public DateTime LastAttackTime { get; set; }
        public bool IsConnected { get; set; }
        public string ConnectionId { get; set; }

        public Player()
        {
            Skills = new List<Skill>();
            Position = new PlayerPosition();
            Health = MaxHealth = 1000;
            IsConnected = false;
        }

        public Player(int id, string name) : this()
        {
            Id = id;
            Name = name;
        }
    }

    public class PlayerPosition
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public PlayerPosition() { }

        public PlayerPosition(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
    }
}
