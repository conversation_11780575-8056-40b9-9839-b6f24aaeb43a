{"version": 3, "targets": {"net9.0": {"CombatTestShared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/CombatTestShared.dll": {}}, "runtime": {"bin/placeholder/CombatTestShared.dll": {}}}}}, "libraries": {"CombatTestShared/1.0.0": {"type": "project", "path": "../CombatTestShared/CombatTestShared.csproj", "msbuildProject": "../CombatTestShared/CombatTestShared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["CombatTestShared >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\test\\Damage\\CombatTestClient\\CombatTestClient.csproj", "projectName": "CombatTestClient", "projectPath": "C:\\Users\\<USER>\\test\\Damage\\CombatTestClient\\CombatTestClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\test\\Damage\\CombatTestClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\test\\Damage\\CombatTestShared\\CombatTestShared.csproj": {"projectPath": "C:\\Users\\<USER>\\test\\Damage\\CombatTestShared\\CombatTestShared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}