using System;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Text.Json;
using CombatTestShared.Models;

namespace CombatTestClient
{
    public class SimulatedClient
    {
        public int PlayerId { get; }
        public bool IsConnected { get; private set; }
        public long AttacksSent { get; private set; }
        public long DamagePacketsReceived { get; private set; }
        public long BytesReceived { get; private set; }
        public long BytesSent { get; private set; }
        public DateTime ConnectedAt { get; private set; }

        private readonly NetworkConfiguration _config;
        private TcpClient _tcpClient;
        private NetworkStream _stream;
        private readonly object _sendLock = new object();
        private CancellationTokenSource _receiveCancellation;

        public event EventHandler<DamagePacket> DamageReceived;
        public event EventHandler<string> StatusChanged;

        public SimulatedClient(int playerId, NetworkConfiguration config)
        {
            PlayerId = playerId;
            _config = config ?? throw new ArgumentNullException(nameof(config));
            IsConnected = false;
        }

        public async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected) return;

            try
            {
                _tcpClient = new TcpClient();
                _tcpClient.NoDelay = !_config.UseNagleAlgorithm;
                _tcpClient.ReceiveTimeout = (int)_config.ConnectionTimeout.TotalMilliseconds;
                _tcpClient.SendTimeout = (int)_config.ConnectionTimeout.TotalMilliseconds;

                // For simulation purposes, we'll simulate a successful connection
                // In a real implementation, this would connect to the actual server
                await Task.Delay(10, cancellationToken); // Simulate connection time

                IsConnected = true;
                ConnectedAt = DateTime.UtcNow;
                
                // Start receiving messages (simulated)
                _receiveCancellation = new CancellationTokenSource();
                _ = Task.Run(() => ReceiveMessagesAsync(_receiveCancellation.Token), cancellationToken);

                StatusChanged?.Invoke(this, $"Client {PlayerId} connected");
            }
            catch (Exception ex)
            {
                IsConnected = false;
                StatusChanged?.Invoke(this, $"Client {PlayerId} connection failed: {ex.Message}");
                throw;
            }
        }

        public async Task DisconnectAsync()
        {
            if (!IsConnected) return;

            try
            {
                IsConnected = false;
                _receiveCancellation?.Cancel();
                
                _stream?.Close();
                _tcpClient?.Close();

                StatusChanged?.Invoke(this, $"Client {PlayerId} disconnected");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Client {PlayerId} disconnect error: {ex.Message}");
            }
        }

        public async Task SendAttackCommandAsync(AttackCommand command)
        {
            if (!IsConnected)
                throw new InvalidOperationException($"Client {PlayerId} is not connected");

            try
            {
                lock (_sendLock)
                {
                    if (!IsConnected) return;

                    // Simulate sending attack command
                    var json = JsonSerializer.Serialize(command);
                    var bytes = Encoding.UTF8.GetBytes(json);
                    
                    // In real implementation, this would send via TCP stream
                    // For simulation, we just track the send operation
                    BytesSent += bytes.Length;
                    AttacksSent++;
                }

                // Simulate network delay
                await Task.Delay(Random.Shared.Next(1, 5));

                StatusChanged?.Invoke(this, $"Client {PlayerId} sent attack command {command.Id}");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Client {PlayerId} send error: {ex.Message}");
                throw;
            }
        }

        private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[_config.BufferSize];
            var random = new Random(PlayerId);

            while (IsConnected && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Simulate receiving damage packets
                    await Task.Delay(random.Next(50, 200), cancellationToken);

                    // Generate simulated damage packet
                    var damagePacket = GenerateSimulatedDamagePacket(random);
                    
                    if (damagePacket != null)
                    {
                        DamagePacketsReceived++;
                        BytesReceived += 100; // Approximate packet size
                        
                        DamageReceived?.Invoke(this, damagePacket);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    if (IsConnected)
                    {
                        StatusChanged?.Invoke(this, $"Client {PlayerId} receive error: {ex.Message}");
                        await Task.Delay(1000, cancellationToken); // Wait before retrying
                    }
                }
            }
        }

        private DamagePacket GenerateSimulatedDamagePacket(Random random)
        {
            // Simulate receiving damage packets based on attack patterns
            if (AttacksSent == 0 || random.NextDouble() > 0.3) // 30% chance of receiving damage
                return null;

            var monsterId = random.Next(1, 1000);
            var damage = random.Next(50, 200);
            var skillId = random.Next(1, 5);
            var hitIndex = random.Next(0, 5);

            return new DamagePacket(PlayerId, monsterId, skillId, hitIndex, damage)
            {
                IsCritical = random.NextDouble() < 0.15,
                Type = DamageType.Physical
            };
        }

        public ClientConnectionInfo GetConnectionInfo()
        {
            return new ClientConnectionInfo
            {
                PlayerId = PlayerId,
                IsConnected = IsConnected,
                ConnectedAt = ConnectedAt,
                AttacksSent = AttacksSent,
                DamagePacketsReceived = DamagePacketsReceived,
                BytesReceived = BytesReceived,
                BytesSent = BytesSent,
                ConnectionDuration = IsConnected ? DateTime.UtcNow - ConnectedAt : TimeSpan.Zero
            };
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
            _receiveCancellation?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Dispose();
        }
    }

    public class ClientConnectionInfo
    {
        public int PlayerId { get; set; }
        public bool IsConnected { get; set; }
        public DateTime ConnectedAt { get; set; }
        public long AttacksSent { get; set; }
        public long DamagePacketsReceived { get; set; }
        public long BytesReceived { get; set; }
        public long BytesSent { get; set; }
        public TimeSpan ConnectionDuration { get; set; }
        public double AttacksPerSecond => ConnectionDuration.TotalSeconds > 0 ? AttacksSent / ConnectionDuration.TotalSeconds : 0;
        public double PacketsPerSecond => ConnectionDuration.TotalSeconds > 0 ? DamagePacketsReceived / ConnectionDuration.TotalSeconds : 0;
    }
}
