using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using CombatTestShared.Models;
using CombatTestShared.Interfaces;

namespace CombatTestPerformance.TestScenarios
{
    public class CombatTestScenario : ITestScenario
    {
        public string Name { get; }
        public TestConfiguration Configuration { get; }

        public CombatTestScenario(string name, TestConfiguration configuration)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public async Task<TestResult> ExecuteAsync(ICombatSystem combatSystem)
        {
            var monitor = new PerformanceMonitor();
            return await ExecuteAsync(combatSystem, monitor);
        }

        public async Task<TestResult> ExecuteAsync(ICombatSystem combatSystem, IPerformanceMonitor monitor)
        {
            var result = new TestResult
            {
                TestName = Name,
                Approach = combatSystem.Approach,
                StartTime = DateTime.UtcNow
            };

            try
            {
                Console.WriteLine($"Starting test scenario: {Name} with {combatSystem.Name}");
                
                // Start monitoring
                monitor.StartMonitoring();
                
                // Start combat system
                await combatSystem.StartAsync();
                
                // Execute the test scenario
                await ExecuteTestLogic(combatSystem, monitor);
                
                // Wait for test duration
                await Task.Delay(Configuration.TestDuration);
                
                result.Success = true;
                Console.WriteLine($"Test scenario completed successfully: {Name}");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"Test scenario failed: {Name} - {ex.Message}");
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                
                // Stop monitoring and generate report
                monitor.StopMonitoring();
                result.PerformanceReport = monitor.GenerateReport(result.Duration);
                result.PerformanceReport.Approach = combatSystem.Approach;
                
                // Stop combat system
                await combatSystem.StopAsync();
            }

            return result;
        }

        private async Task ExecuteTestLogic(ICombatSystem combatSystem, IPerformanceMonitor monitor)
        {
            var random = new Random();
            var attackTasks = new List<Task>();
            
            // Generate attack commands for all players
            for (int playerId = 1; playerId <= Configuration.PlayerCount; playerId++)
            {
                var playerTask = SimulatePlayerAttacks(playerId, combatSystem, monitor, random);
                attackTasks.Add(playerTask);
                
                // Add small delay between player starts to simulate realistic connection timing
                if (playerId % 50 == 0) // Every 50 players
                {
                    await Task.Delay(10);
                }
            }

            // Wait for all player simulations to complete
            await Task.WhenAll(attackTasks);
        }

        private async Task SimulatePlayerAttacks(int playerId, ICombatSystem combatSystem, 
            IPerformanceMonitor monitor, Random random)
        {
            var endTime = DateTime.UtcNow.Add(Configuration.TestDuration);
            
            while (DateTime.UtcNow < endTime)
            {
                try
                {
                    // Generate random skill and targets
                    var skillId = random.Next(1, Configuration.SkillsPerPlayer + 1);
                    var targetIds = GenerateRandomTargets(playerId, random);
                    
                    var attackCommand = new AttackCommand(playerId, skillId, targetIds)
                    {
                        Approach = combatSystem.Approach
                    };
                    
                    // Record the attack command
                    monitor.RecordAttackCommand(attackCommand);
                    
                    // Execute attack (don't await to allow concurrent attacks)
                    var attackTask = ExecuteAttackWithMonitoring(combatSystem, attackCommand, monitor);
                    
                    // Wait for attack interval before next attack
                    await Task.Delay(Configuration.AttackInterval);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in player {playerId} simulation: {ex.Message}");
                    await Task.Delay(1000); // Wait before retrying
                }
            }
        }

        private async Task ExecuteAttackWithMonitoring(ICombatSystem combatSystem, 
            AttackCommand command, IPerformanceMonitor monitor)
        {
            try
            {
                // Subscribe to damage events to record packets
                EventHandler<DamagePacket> damageHandler = (sender, packet) =>
                {
                    monitor.RecordDamagePacket(packet);
                };
                
                combatSystem.DamageDealt += damageHandler;
                
                // Execute the attack
                var result = await combatSystem.ExecuteAttackAsync(command);
                
                // Record system event
                monitor.RecordSystemEvent("AttackCompleted", new 
                { 
                    AttackId = command.Id,
                    PlayerId = command.PlayerId,
                    TotalDamage = result.TotalDamageDealt,
                    ExecutionTime = result.ExecutionTime.TotalMilliseconds
                });
                
                combatSystem.DamageDealt -= damageHandler;
            }
            catch (Exception ex)
            {
                monitor.RecordSystemEvent("AttackFailed", new 
                { 
                    AttackId = command.Id,
                    PlayerId = command.PlayerId,
                    Error = ex.Message
                });
            }
        }

        private int[] GenerateRandomTargets(int playerId, Random random)
        {
            var targetCount = Configuration.MonsterCountPerPlayer;
            var targets = new int[targetCount];
            
            // Generate unique monster IDs for this player
            // Each player targets different monsters to avoid conflicts
            var baseMonsterIndex = (playerId - 1) * targetCount;
            
            for (int i = 0; i < targetCount; i++)
            {
                targets[i] = baseMonsterIndex + i + 1;
            }
            
            return targets;
        }
    }

    public static class TestScenarioFactory
    {
        public static ITestScenario CreateStandardLoadTest()
        {
            var config = new TestConfiguration
            {
                PlayerCount = 500,
                MonsterCountPerPlayer = 10,
                SkillsPerPlayer = 3,
                TestDuration = TimeSpan.FromMinutes(2),
                AttackInterval = TimeSpan.FromSeconds(3),
                EnablePerformanceMonitoring = true,
                EnableDetailedLogging = false
            };
            
            return new CombatTestScenario("Standard Load Test", config);
        }

        public static ITestScenario CreateHighLoadTest()
        {
            var config = new TestConfiguration
            {
                PlayerCount = 1000,
                MonsterCountPerPlayer = 10,
                SkillsPerPlayer = 5,
                TestDuration = TimeSpan.FromMinutes(3),
                AttackInterval = TimeSpan.FromSeconds(2),
                EnablePerformanceMonitoring = true,
                EnableDetailedLogging = false
            };
            
            return new CombatTestScenario("High Load Test", config);
        }

        public static ITestScenario CreateStressTest()
        {
            var config = new TestConfiguration
            {
                PlayerCount = 1000,
                MonsterCountPerPlayer = 15,
                SkillsPerPlayer = 5,
                TestDuration = TimeSpan.FromMinutes(5),
                AttackInterval = TimeSpan.FromSeconds(1),
                EnablePerformanceMonitoring = true,
                EnableDetailedLogging = true
            };
            
            return new CombatTestScenario("Stress Test", config);
        }

        public static ITestScenario CreateCustomTest(TestConfiguration configuration)
        {
            return new CombatTestScenario("Custom Test", configuration);
        }
    }
}
