using System;
using System.Collections.Generic;
using CombatTestShared.Models;

namespace CombatTestPerformance
{
    public class ComparisonReport
    {
        public DateTime GeneratedAt { get; set; }
        public TimeSpan TotalTestDuration { get; set; }
        public int TotalTests { get; set; }
        public int SuccessfulTests { get; set; }
        public int FailedTests { get; set; }
        public List<ApproachComparison> ApproachComparisons { get; set; }
        public AttackApproach BestCpuEfficiency { get; set; }
        public AttackApproach BestMemoryEfficiency { get; set; }
        public AttackApproach BestLatency { get; set; }
        public AttackApproach BestThroughput { get; set; }
        public string Recommendations { get; set; }

        public ComparisonReport()
        {
            ApproachComparisons = new List<ApproachComparison>();
            GeneratedAt = DateTime.UtcNow;
        }
    }

    public class ApproachComparison
    {
        public AttackApproach Approach { get; set; }
        public int TestCount { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public long TotalDamagePackets { get; set; }
        public long TotalAttacks { get; set; }
        public double AverageCpuUsage { get; set; }
        public double PeakCpuUsage { get; set; }
        public long AverageMemoryUsage { get; set; }
        public long PeakMemoryUsage { get; set; }
        public double AverageLatency { get; set; }
        public double AverageThroughput { get; set; }
        public double EfficiencyScore { get; set; }
        public string Strengths { get; set; }
        public string Weaknesses { get; set; }
    }
}
